import { <PERSON>, Get, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { AdminFileService } from '../services/admin-file.service';
import { FileQueryDto, FileResponseDto, FileDetailResponseDto, FolderInfoDto } from '../dto/file';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { FILE_ERROR_CODES } from '../exceptions/file.exception';

/**
 * Controller xử lý các API liên quan đến file cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS_FILE)
@ApiExtraModels(ApiResponseDto, FileResponseDto, FileDetailResponseDto, FolderInfoDto, PaginatedResult)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/files')
export class AdminFileController {
  constructor(private readonly adminFileService: AdminFileService) {}

  /**
   * Lấy danh sách file với phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách file với phân trang và tìm kiếm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách file',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    FILE_ERROR_CODES.FILE_FIND_FAILED,
  )
  async findAll(
    @Query() queryDto: FileQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<FileResponseDto>>> {
    const result = await this.adminFileService.findAll(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết file theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết file theo ID' })
  @ApiParam({ name: 'id', description: 'ID của file', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết file',
    schema: ApiResponseDto.getSchema(FileDetailResponseDto),
  })
  @ApiErrorResponse(
    FILE_ERROR_CODES.FILE_NOT_FOUND,
    FILE_ERROR_CODES.FILE_DETAIL_FAILED,
  )
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<FileDetailResponseDto>> {
    const result = await this.adminFileService.findById(id);
    return ApiResponseDto.success(result);
  }
}
