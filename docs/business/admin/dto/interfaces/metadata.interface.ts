import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * Class cho dữ liệu trường tùy chỉnh của khách hàng
 */
export class MetadataField {
  /**
   * Tên trường
   */
  @ApiProperty({
    description: 'Tên trường',
    example: 'address'
  })
  @IsNotEmpty()
  @IsString()
  fieldName: string;

  /**
   * Gi<PERSON> trị trường
   */
  @ApiProperty({
    description: '<PERSON>i<PERSON> trị trường',
    example: 'Hà Nội'
  })
  @IsNotEmpty()
  fieldValue: string | number | boolean | string[] | number[];
}
