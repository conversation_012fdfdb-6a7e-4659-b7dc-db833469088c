import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserConvertCustomerDto } from '../../dto/userconverts/query-user-convert-customer.dto';

describe('QueryUserConvertCustomerDto', () => {
  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {
      page: 1,
      limit: 10,
      search: 'Nguyễn',
      userId: 123,
      agentId: '550e8400-e29b-41d4-a716-446655440000',
      platform: 'Facebook',
      createdAtFrom: 1625097600000,
      createdAtTo: 1625184000000,
      sortBy: 'createdAt',
      sortDirection: 'DESC',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên chuyển đổi các giá trị từ chuỗi sang số', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {
      page: '1',
      limit: '10',
      userId: '123',
      createdAtFrom: '1625097600000',
      createdAtTo: '1625184000000',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.userId).toBe(123);
    expect(dto.createdAtFrom).toBe(1625097600000);
    expect(dto.createdAtTo).toBe(1625184000000);
  });

  it('nên thất bại khi page là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {
      page: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('page');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {
      limit: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('limit');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi userId là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {
      userId: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userId');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên thất bại khi agentId không phải là UUID hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserConvertCustomerDto, {
      agentId: 'invalid-uuid',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('agentId');
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });
});
