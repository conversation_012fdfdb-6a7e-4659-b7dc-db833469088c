import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsPhoneNumber, IsString, MaxLength, IsNumber } from 'class-validator';
import { Expose } from 'class-transformer';

/**
 * DTO cho thông tin cửa hàng của người dùng
 */
export class UserShopInfoDto {
  @ApiProperty({
    description: 'Tên cửa hàng',
    example: 'Cửa hàng ABC',
    maxLength: 255
  })
  @IsNotEmpty({ message: 'Tên cửa hàng không được để trống' })
  @IsString({ message: 'Tên cửa hàng phải là chuỗi' })
  @MaxLength(255, { message: 'Tên cửa hàng không được vượt quá 255 ký tự' })
  @Expose()
  shopName: string;

  @ApiProperty({
    description: '<PERSON><PERSON> điện thoại cửa hàng',
    example: '0123456789'
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> điện thoại không được để trống' })
  @IsPhoneNumber('VN', { message: '<PERSON><PERSON> điện thoại không hợp lệ' })
  @Expose()
  shopPhone: string;

  @ApiProperty({
    description: 'Địa chỉ cửa hàng',
    example: 'Số 123, Đường ABC, Phường 1, Quận 1, TP.HCM',
    maxLength: 500
  })
  @IsNotEmpty({ message: 'Địa chỉ cửa hàng không được để trống' })
  @IsString({ message: 'Địa chỉ cửa hàng phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ cửa hàng không được vượt quá 500 ký tự' })
  @Expose()
  shopAddress: string;

  @ApiProperty({
    description: 'Tỉnh/Thành phố',
    example: 'Hồ Chí Minh',
    maxLength: 100
  })
  @IsNotEmpty({ message: 'Tỉnh/Thành phố không được để trống' })
  @IsString({ message: 'Tỉnh/Thành phố phải là chuỗi' })
  @MaxLength(100, { message: 'Tỉnh/Thành phố không được vượt quá 100 ký tự' })
  @Expose()
  shopProvince: string;

  @ApiProperty({
    description: 'Quận/Huyện',
    example: 'Quận 1',
    maxLength: 100
  })
  @IsNotEmpty({ message: 'Quận/Huyện không được để trống' })
  @IsString({ message: 'Quận/Huyện phải là chuỗi' })
  @MaxLength(100, { message: 'Quận/Huyện không được vượt quá 100 ký tự' })
  @Expose()
  shopDistrict: string;

  @ApiProperty({
    description: 'Phường/Xã',
    example: 'Phường Bến Nghé',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Phường/Xã phải là chuỗi' })
  @MaxLength(100, { message: 'Phường/Xã không được vượt quá 100 ký tự' })
  @Expose()
  shopWard?: string;
}

/**
 * DTO response cho thông tin cửa hàng
 */
export class UserShopInfoResponseDto extends UserShopInfoDto {
  @ApiProperty({
    description: 'ID của shop',
    example: 1
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID người dùng',
    example: 1
  })
  @Expose()
  userId: number;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1641708800000
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1641708800000
  })
  @Expose()
  updatedAt: number;
}

/**
 * DTO cập nhật thông tin cửa hàng
 */
export class UpdateUserShopInfoDto {
  @ApiProperty({
    description: 'Tên cửa hàng',
    example: 'Cửa hàng ABC',
    maxLength: 255,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Tên cửa hàng phải là chuỗi' })
  @MaxLength(255, { message: 'Tên cửa hàng không được vượt quá 255 ký tự' })
  @Expose()
  shopName?: string;

  @ApiProperty({
    description: 'Số điện thoại cửa hàng',
    example: '0123456789',
    required: false
  })
  @IsOptional()
  @IsPhoneNumber('VN', { message: 'Số điện thoại không hợp lệ' })
  @Expose()
  shopPhone?: string;

  @ApiProperty({
    description: 'Địa chỉ cửa hàng',
    example: 'Số 123, Đường ABC, Phường 1, Quận 1, TP.HCM',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ cửa hàng phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ cửa hàng không được vượt quá 500 ký tự' })
  @Expose()
  shopAddress?: string;

  @ApiProperty({
    description: 'Tỉnh/Thành phố',
    example: 'Hồ Chí Minh',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Tỉnh/Thành phố phải là chuỗi' })
  @MaxLength(100, { message: 'Tỉnh/Thành phố không được vượt quá 100 ký tự' })
  @Expose()
  shopProvince?: string;

  @ApiProperty({
    description: 'Quận/Huyện',
    example: 'Quận 1',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Quận/Huyện phải là chuỗi' })
  @MaxLength(100, { message: 'Quận/Huyện không được vượt quá 100 ký tự' })
  @Expose()
  shopDistrict?: string;

  @ApiProperty({
    description: 'Phường/Xã',
    example: 'Phường Bến Nghé',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Phường/Xã phải là chuỗi' })
  @MaxLength(100, { message: 'Phường/Xã không được vượt quá 100 ký tự' })
  @Expose()
  shopWard?: string;
}
