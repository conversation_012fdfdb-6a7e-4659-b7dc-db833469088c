import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsArray, ValidateNested } from 'class-validator';
import { CustomerFacebookDto, CustomerFacebookResponseDto } from './customer-facebook.dto';
import { CustomerWebDto, CustomerWebResponseDto } from './customer-web.dto';

/**
 * DTO cho cập nhật thông tin social của khách hàng chuyển đổi
 */
export class UpdateCustomerSocialDto {
  @ApiProperty({
    description: 'Danh sách thông tin Facebook của khách hàng',
    type: [CustomerFacebookDto],
    required: false,
    example: [
      {
        pageScopedId: '12345678901234567890',
        pageId: '123456789012345',
        name: '<PERSON><PERSON><PERSON>n <PERSON>ăn <PERSON>',
        avatar: 'https://example.com/avatar.jpg',
        gender: 'male'
      }
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Facebook data phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => CustomerFacebookDto)
  facebook?: CustomerFacebookDto[];

  @ApiProperty({
    description: 'Danh sách thông tin Web của khách hàng',
    type: [CustomerWebDto],
    required: false,
    example: [
      {
        domain: 'example.com',
        path: '/products/123',
        device: 'Desktop',
        os: 'Windows 10',
        ip: '***********',
        browser: 'Chrome 91.0.4472.124',
        startSessionUnix: 1625097600000,
        endSessionUnix: 1625101200000,
        favicon: 'https://example.com/favicon.ico'
      }
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Web data phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => CustomerWebDto)
  web?: CustomerWebDto[];
}

/**
 * DTO response cho thông tin social của khách hàng chuyển đổi
 */
export class CustomerSocialResponseDto {
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 123,
  })
  customerId: number;

  @ApiProperty({
    description: 'Danh sách thông tin Facebook của khách hàng',
    type: [CustomerFacebookResponseDto],
    example: [
      {
        id: 1,
        pageScopedId: '12345678901234567890',
        pageId: '123456789012345',
        name: 'Nguyễn Văn A',
        avatar: 'https://example.com/avatar.jpg',
        gender: 'male',
        userConvertCustomerId: 123
      }
    ],
  })
  facebook: CustomerFacebookResponseDto[];

  @ApiProperty({
    description: 'Danh sách thông tin Web của khách hàng',
    type: [CustomerWebResponseDto],
    example: [
      {
        id: 1,
        domain: 'example.com',
        path: '/products/123',
        device: 'Desktop',
        os: 'Windows 10',
        ip: '***********',
        browser: 'Chrome 91.0.4472.124',
        startSessionUnix: 1625097600000,
        endSessionUnix: 1625101200000,
        favicon: 'https://example.com/favicon.ico',
        userConvertCustomerId: 123
      }
    ],
  })
  web: CustomerWebResponseDto[];
}
