# GHTK Environment Configuration

## Environment Variables

Đ<PERSON> sử dụng GHTK Service, bạn cần cấu hình các environment variables sau:

### Required Variables

```bash
# GHTK API Token (bắt buộc)
GHTK_TOKEN=your_ghtk_api_token_here

# GHTK Partner Code (tùy chọn)
GHTK_PARTNER_CODE=your_partner_code_here
```

### Optional Variables

```bash
# GHTK Base URL (mặc định: https://services.ghtk.vn)
GHTK_BASE_URL=https://services.ghtk.vn

# GHTK Request Timeout in milliseconds (mặc định: 30000)
GHTK_TIMEOUT=30000

# Test Mode (mặc định: true)
GHTK_TEST_MODE=true
```

## Cấu hình cho các môi trường

### Development (.env.development)

```bash
# GHTK Test Configuration (LƯU Ý: Token test có thể đã hết hạn)
# Nên sử dụng token thực tế từ tài khoản GHTK
GHTK_TOKEN=your_real_ghtk_token_here
GHTK_PARTNER_CODE=your_real_partner_code_here
GHTK_BASE_URL=https://services.ghtk.vn
GHTK_TIMEOUT=30000
GHTK_TEST_MODE=true
```

### Staging (.env.staging)

```bash
# GHTK Staging Configuration
GHTK_TOKEN=your_staging_token_here
GHTK_PARTNER_CODE=your_staging_partner_code
GHTK_BASE_URL=https://services.ghtk.vn
GHTK_TIMEOUT=30000
GHTK_TEST_MODE=true
```

### Production (.env.production)

```bash
# GHTK Production Configuration
GHTK_TOKEN=your_production_token_here
GHTK_PARTNER_CODE=your_production_partner_code
GHTK_BASE_URL=https://services.ghtk.vn
GHTK_TIMEOUT=30000
GHTK_TEST_MODE=false
```

## Lấy Token GHTK

### 1. Đăng ký tài khoản GHTK

1. Truy cập [https://khachhang.ghtk.vn/](https://khachhang.ghtk.vn/)
2. Đăng ký tài khoản shop
3. Xác thực thông tin

### 2. Lấy API Token

1. Đăng nhập vào tài khoản GHTK
2. Vào mục **Cài đặt** > **API**
3. Copy **Token** và **Partner Code**

### 3. Test Token

**⚠️ LƯU Ý QUAN TRỌNG:** Token test từ tài liệu GHTK có thể đã hết hạn hoặc không hoạt động.

**Khuyến nghị:** Đăng ký tài khoản GHTK thực tế và sử dụng token thực:

```bash
# Token thực tế từ tài khoản GHTK (KHUYẾN NGHỊ)
GHTK_TOKEN=your_real_token_from_ghtk_account
GHTK_PARTNER_CODE=your_real_partner_code

# Token test (có thể không hoạt động)
# GHTK_TOKEN=APITokenSample-ca441e70288cB0515F310742
# GHTK_PARTNER_CODE=PARTNER_CODE_SAMPLE
```

## Validation

Service sẽ tự động validate cấu hình khi khởi tạo:

```typescript
this.logger.log('GHTK Service initialized', {
  baseUrl: this.config.baseUrl,
  isTestMode: this.config.isTestMode,
  hasToken: !!this.config.token,
  hasPartnerCode: !!this.config.partnerCode
});
```

## Fallback Values

Nếu không có environment variables, service sẽ sử dụng giá trị mặc định:

```typescript
{
  token: GHTK_TEST_CONFIG.TOKEN,
  partnerCode: GHTK_TEST_CONFIG.PARTNER_CODE,
  baseUrl: GHTK_TEST_CONFIG.BASE_URL,
  timeout: GHTK_TIMEOUT,
  isTestMode: true
}
```

## Security Best Practices

### 1. Không commit token vào git

Thêm vào `.gitignore`:

```gitignore
# Environment files
.env
.env.local
.env.development
.env.staging
.env.production
```

### 2. Sử dụng secret management

Trong production, sử dụng:
- AWS Secrets Manager
- Azure Key Vault
- Google Secret Manager
- Kubernetes Secrets

### 3. Rotate tokens định kỳ

- Thay đổi token GHTK định kỳ
- Update environment variables
- Restart services

## Docker Configuration

### docker-compose.yml

```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - GHTK_TOKEN=${GHTK_TOKEN}
      - GHTK_PARTNER_CODE=${GHTK_PARTNER_CODE}
      - GHTK_BASE_URL=${GHTK_BASE_URL}
      - GHTK_TIMEOUT=${GHTK_TIMEOUT}
      - GHTK_TEST_MODE=${GHTK_TEST_MODE}
    env_file:
      - .env
```

### Dockerfile

```dockerfile
# Environment variables can be set at build time
ARG GHTK_TOKEN
ARG GHTK_PARTNER_CODE

ENV GHTK_TOKEN=${GHTK_TOKEN}
ENV GHTK_PARTNER_CODE=${GHTK_PARTNER_CODE}
```

## Kubernetes Configuration

### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ghtk-config
data:
  GHTK_BASE_URL: "https://services.ghtk.vn"
  GHTK_TIMEOUT: "30000"
  GHTK_TEST_MODE: "false"
```

### Secret

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ghtk-secret
type: Opaque
data:
  GHTK_TOKEN: <base64-encoded-token>
  GHTK_PARTNER_CODE: <base64-encoded-partner-code>
```

### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
spec:
  template:
    spec:
      containers:
      - name: app
        image: your-app:latest
        envFrom:
        - configMapRef:
            name: ghtk-config
        - secretRef:
            name: ghtk-secret
```

## Monitoring & Alerts

### Health Check

Service tự động log trạng thái cấu hình:

```typescript
// Kiểm tra token có hợp lệ không
if (!this.config.token) {
  this.logger.warn('GHTK token not configured, using test token');
}

// Kiểm tra partner code
if (!this.config.partnerCode) {
  this.logger.warn('GHTK partner code not configured');
}
```

### Alerts

Thiết lập alerts cho:
- Token sắp hết hạn
- API rate limit exceeded
- Connection failures
- Invalid configuration

## Troubleshooting

### Common Issues

1. **Token không hợp lệ hoặc đã hết hạn (Code: 10016)**
   ```json
   {
     "code": 10016,
     "message": "Token không hợp lệ hoặc đã hết hạn"
   }
   ```
   **Giải pháp:**
   - Token test từ tài liệu GHTK có thể đã hết hạn
   - Đăng ký tài khoản GHTK thực tế tại: https://khachhang.ghtk.vn/
   - Lấy token thực từ **Cài đặt** > **API** trong dashboard GHTK
   - Cập nhật `GHTK_TOKEN` trong file `.env`

2. **Token không hợp lệ (HTTP 401)**
   ```
   Error: GHTK_INVALID_TOKEN - Token GHTK không hợp lệ
   ```
   - Kiểm tra token trong environment variables
   - Verify token trên GHTK dashboard

2. **Partner code sai**
   ```
   Error: GHTK_INVALID_PARTNER_CODE - Mã đối tác GHTK không hợp lệ
   ```
   - Kiểm tra partner code
   - Liên hệ GHTK support

3. **Network issues**
   ```
   Error: GHTK_NETWORK_ERROR - Lỗi kết nối mạng với GHTK
   ```
   - Kiểm tra internet connection
   - Verify GHTK API status

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug
GHTK_TEST_MODE=true
```

Service sẽ log chi tiết tất cả requests/responses.
