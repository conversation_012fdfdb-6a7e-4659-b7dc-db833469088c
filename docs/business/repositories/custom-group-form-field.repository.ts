import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { CustomGroupFormField } from '@modules/business/entities';

/**
 * Repository xử lý truy vấn dữ liệu cho entity CustomGroupFormField
 */
@Injectable()
export class CustomGroupFormFieldRepository extends Repository<CustomGroupFormField> {
  protected readonly logger = new Logger(CustomGroupFormFieldRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomGroupFormField, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho CustomGroupFormField trong admin
   * @returns SelectQueryBuilder<CustomGroupFormField>
   */
  protected createBaseQuery(): SelectQueryBuilder<CustomGroupFormField> {
    return this.createQueryBuilder('customGroupFormField');
  }

  /**
   * Tìm danh sách trường trong nhóm
   * @param formGroupId ID của nhóm trường
   * @returns Danh sách trường trong nhóm
   */
  async findByFormGroupId(formGroupId: number): Promise<CustomGroupFormField[]> {
    try {
      return await this.find({ where: { formGroupId } });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường trong nhóm theo formGroupId ${formGroupId}: ${error.message}`);
      throw new Error(`Lỗi khi lấy danh sách trường trong nhóm theo formGroupId ${formGroupId}: ${error.message}`);
    }
  }

  /**
   * Tìm trường trong nhóm theo ID trường và ID nhóm
   * @param fieldId ID của trường tùy chỉnh
   * @param formGroupId ID của nhóm trường tùy chỉnh
   * @returns Trường trong nhóm hoặc null nếu không tìm thấy
   */
  async findByFieldIdAndFormGroupId(
    fieldId: number,
    formGroupId: number,
  ): Promise<CustomGroupFormField | null> {
    try {
      return await this.findOne({ where: { fieldId, formGroupId } });
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm trường trong nhóm theo fieldId ${fieldId} và formGroupId ${formGroupId}: ${error.message}`,
      );
      throw new Error(`Lỗi khi tìm trường trong nhóm theo fieldId ${fieldId} và formGroupId ${formGroupId}: ${error.message}`);
    }
  }

  /**
   * Tìm và tạo map giá trị trường theo formGroupId
   * @param formGroupId ID của nhóm trường
   * @returns Map chứa giá trị của các trường theo fieldId
   */
  async findFieldValuesMapByFormGroupId(formGroupId: number): Promise<Record<number, any>> {
    const fields = await this.findByFormGroupId(formGroupId);

    return fields.reduce((map, field) => {
      map[field.fieldId] = field.value;
      return map;
    }, {} as Record<number, any>);
  }
}