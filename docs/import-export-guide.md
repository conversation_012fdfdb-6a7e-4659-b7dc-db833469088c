# Quy tắc Import và Export trong dự án

## Giới thiệu

Tài liệu này mô tả các quy tắc và tiêu chuẩn cho việc import và export trong dự án. Việc tuân thủ các quy tắc này sẽ giúp code dễ đọc, dễ bảo trì và nhất quán trong toàn bộ dự án.

> **LƯU Ý QUAN TRỌNG**: Việc tuân thủ các quy tắc import/export là bắt buộc để đảm bảo tính nhất quán và dễ bảo trì của code. Các pull request không tuân thủ quy tắc này sẽ không được chấp nhận.

## Cấu trúc Export trong index.ts

### Nguyên tắc chung

1. **Mỗi module nên có một file `index.ts`** để export tất cả các thành phần cần thiết
2. **Sử dụng named exports** thay vì default exports khi có thể
3. **Phân nhóm các export** theo loại (components, hooks, types, services, v.v.)
4. **Sử dụng comment** để phân tách các nhóm export

### Mẫu file index.ts cho module

```typescript
// Export components
export { default as ComponentA } from './components/ComponentA';
export { default as ComponentB } from './components/ComponentB';
export { ComponentC, ComponentD } from './components/ComponentCD';

// Export pages
export { default as PageA } from './pages/PageA';
export { default as PageB } from './pages/PageB';

// Export hooks
export { useHookA, useHookB } from './hooks/useHookA';
export { useHookC } from './hooks/useHookC';

// Export types
export * from './types/module.types';

// Export services
export * from './services/module.service';

// Export routes
export { default as moduleRoutes } from './routes/moduleRoutes';
```

### Export từ thư mục con

Khi một thư mục con có nhiều component, nên tạo một file `index.ts` trong thư mục đó:

```typescript
// src/modules/module-name/components/index.ts
export { default as ComponentA } from './ComponentA';
export { default as ComponentB } from './ComponentB';
export { default as ComponentC } from './ComponentC';

// Sau đó trong src/modules/module-name/index.ts
export * from './components';
```

### Re-export từ thư viện bên ngoài

Khi cần re-export từ thư viện bên ngoài, nên tạo một file riêng:

```typescript
// src/shared/api/index.ts
export * from './axios';
export * from './query-client';
export * from './hooks';

// Re-export các hàm thường dùng từ @tanstack/react-query
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  useSuspenseQuery,
  useSuspenseInfiniteQuery,
  useIsFetching,
  useIsMutating,
} from '@tanstack/react-query';
```

## Quy tắc Import

### Thứ tự import

Sắp xếp các import theo thứ tự sau:

1. **Thư viện React và các thư viện bên ngoài**
2. **Các module từ dự án** (sử dụng alias `@/`)
3. **Các file trong cùng thư mục** (sử dụng đường dẫn tương đối)
4. **Các file CSS/SCSS**

> **LƯU Ý**: Để đảm bảo tính nhất quán, nên sử dụng extension VSCode "Import Sorter" để tự động sắp xếp imports theo quy tắc.

```typescript
// 1. Thư viện React và các thư viện bên ngoài
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';

// 2. Các module từ dự án (sử dụng alias @/)
import { Button, Card, Typography } from '@/shared/components/common';
import { usePermission } from '@/modules/auth/hooks/usePermission';
import { Permission } from '@/modules/auth/types/permission.types';

// 3. Các file trong cùng thư mục
import { ComponentA } from './ComponentA';
import { useLocalHook } from './hooks';

// 4. Các file CSS/SCSS
import './styles.scss';
```

### Sử dụng alias

Dự án đã cấu hình các alias trong `vite.config.ts`. Nên sử dụng các alias này để import:

```typescript
// ĐÚNG
import { Button } from '@/shared/components/common';
import { usePermission } from '@/modules/auth/hooks';

// KHÔNG NÊN
import { Button } from '../../../shared/components/common';
import { usePermission } from '../../../modules/auth/hooks';
```

### Import từ shared components

Luôn import các shared components từ đường dẫn chung:

```typescript
// ĐÚNG
import { Button, Card, Typography } from '@/shared/components/common';

// KHÔNG NÊN
import Button from '@/shared/components/common/Button';
import Card from '@/shared/components/common/Card';
import Typography from '@/shared/components/common/Typography';
```

### Import từ module khác

Khi import từ module khác, nên import từ entry point của module:

```typescript
// ĐÚNG
import { usePermission, Permission } from '@/modules/auth';
import { LinkedAccountsCard } from '@/modules/integration';

// KHÔNG NÊN
import { usePermission } from '@/modules/auth/hooks/usePermission';
import { Permission } from '@/modules/auth/types/permission.types';
import { LinkedAccountsCard } from '@/modules/integration/components/LinkedAccountsCard';
```

### Lazy loading

Sử dụng lazy loading cho các component trang để tối ưu hiệu suất:

```typescript
import { Suspense, lazy } from 'react';
import { Loading } from '@/shared/components/common';

// Lazy load component
const UserManagementPage = lazy(() => import('@/modules/admin/user/pages/UserManagementPage'));

// Sử dụng với Suspense
const RoutesComponent = () => (
  <Routes>
    <Route
      path="/admin/users"
      element={
        <Suspense fallback={<Loading />}>
          <UserManagementPage />
        </Suspense>
      }
    />
  </Routes>
);
```

## Quy tắc đặt tên

### Tên file và tên export

1. **Component**: Sử dụng PascalCase cho tên file và tên export
   ```typescript
   // UserCard.tsx
   export default UserCard;
   ```

2. **Hook**: Sử dụng camelCase và bắt đầu bằng "use"
   ```typescript
   // useUserData.ts
   export const useUserData = () => { ... };
   ```

3. **Utility**: Sử dụng camelCase
   ```typescript
   // string-utils.ts
   export const formatString = () => { ... };
   ```

4. **Type/Interface**: Sử dụng PascalCase
   ```typescript
   // user.types.ts
   export interface UserData { ... }
   ```

### Default export vs Named export

1. **Default export**: Sử dụng cho component chính của file
   ```typescript
   // UserCard.tsx
   const UserCard = () => { ... };
   export default UserCard;
   ```

2. **Named export**: Sử dụng cho các function, type, constant
   ```typescript
   // user-utils.ts
   export const formatUserName = () => { ... };
   export const getUserInitials = () => { ... };
   ```

## Ví dụ thực tế

### Module structure

```
src/modules/user/
├── components/
│   ├── UserCard.tsx
│   ├── UserList.tsx
│   ├── UserForm.tsx
│   └── index.ts
├── hooks/
│   ├── useUserData.ts
│   ├── useUserMutation.ts
│   └── index.ts
├── pages/
│   ├── UserListPage.tsx
│   ├── UserDetailPage.tsx
│   └── index.ts
├── services/
│   └── user.service.ts
├── types/
│   └── user.types.ts
└── index.ts
```

### File index.ts trong module

```typescript
// src/modules/user/index.ts

// Export components
export { UserCard, UserList, UserForm } from './components';

// Export pages
export { UserListPage, UserDetailPage } from './pages';

// Export hooks
export { useUserData, useUserMutation } from './hooks';

// Export types
export * from './types/user.types';

// Export services
export * from './services/user.service';
```

### File index.ts trong thư mục con

```typescript
// src/modules/user/components/index.ts
export { default as UserCard } from './UserCard';
export { default as UserList } from './UserList';
export { default as UserForm } from './UserForm';
```

## Các trường hợp đặc biệt

### Circular Dependencies

Tránh circular dependencies (phụ thuộc vòng tròn) bằng cách:

1. **Tái cấu trúc code**: Tách các phụ thuộc thành các module riêng biệt
2. **Sử dụng interface**: Định nghĩa interface trong một file riêng
3. **Lazy loading**: Import động khi cần thiết

```typescript
// KHÔNG NÊN
// fileA.ts
import { functionB } from './fileB';
export const functionA = () => {
  return functionB();
};

// fileB.ts
import { functionA } from './fileA';
export const functionB = () => {
  return functionA();
};

// NÊN
// types.ts
export interface ResultType {
  value: string;
}

// fileA.ts
import { ResultType } from './types';
export const functionA = (callback: () => ResultType): ResultType => {
  return callback();
};

// fileB.ts
import { functionA } from './fileA';
import { ResultType } from './types';
export const functionB = (): ResultType => {
  return { value: 'B result' };
};
```

### Dynamic Imports

Sử dụng dynamic imports để tối ưu hiệu suất:

```typescript
// Thay vì import tất cả
import { heavyFunction } from './heavy-module';

// Sử dụng dynamic import
const loadHeavyFunction = async () => {
  const { heavyFunction } = await import('./heavy-module');
  return heavyFunction();
};
```

### Barrel Exports

Sử dụng "barrel exports" để đơn giản hóa imports:

```typescript
// src/shared/utils/index.ts
export * from './string-utils';
export * from './date-utils';
export * from './array-utils';
export * from './object-utils';

// Sử dụng
import { formatDate, slugify, removeDuplicates } from '@/shared/utils';
```

## Các lỗi thường gặp và cách khắc phục

### 1. Import quá nhiều từ một module

**Vấn đề**:
```typescript
import {
  Button, Card, Typography, Input, Select, Checkbox,
  Radio, DatePicker, TimePicker, Switch, Slider,
  // ... nhiều component khác
} from '@/shared/components/common';
```

**Giải pháp**:
```typescript
// Chỉ import những gì cần thiết
import { Button, Card, Typography } from '@/shared/components/common';
```

### 2. Import trực tiếp từ node_modules

**Vấn đề**:
```typescript
import { UserIcon } from '@heroicons/react/24/outline';
```

**Giải pháp**:
```typescript
import { Icon } from '@/shared/components/common';
// Sử dụng: <Icon name="user" />
```

### 3. Import lồng nhau quá sâu

**Vấn đề**:
```typescript
import { SomeComponent } from '@/modules/module-name/components/sub-components/nested-folder/SomeComponent';
```

**Giải pháp**:
```typescript
// Tạo index.ts trong các thư mục con để re-export
// Sau đó import từ cấp cao hơn
import { SomeComponent } from '@/modules/module-name/components';
```

### 4. Không sử dụng lazy loading cho các trang

**Vấn đề**:
```typescript
import HeavyPage from '@/modules/module-name/pages/HeavyPage';
```

**Giải pháp**:
```typescript
const HeavyPage = lazy(() => import('@/modules/module-name/pages/HeavyPage'));
```

## Quy tắc cụ thể cho dự án

### Quy tắc cho shared components

1. **Không import trực tiếp từ thư viện icon bên ngoài**:
   ```typescript
   // ĐÚNG
   import { Icon } from '@/shared/components/common';
   <Icon name="user" />

   // SAI
   import { UserIcon } from '@heroicons/react/24/outline';
   <UserIcon />
   ```

2. **Sử dụng Loading component thay vì Spinner**:
   ```typescript
   // ĐÚNG
   import { Loading } from '@/shared/components/common';
   <Loading />

   // SAI
   import { Spinner } from 'some-library';
   <Spinner />
   ```

3. **Sử dụng các hook từ shared/hooks**:
   ```typescript
   // ĐÚNG
   import { useMediaQuery, useIsMobile } from '@/shared/hooks/common';

   // SAI
   import { useMediaQuery } from 'react-responsive';
   ```

### Quy tắc cho module structure

1. **Mỗi module phải có file index.ts**:
   ```
   src/modules/module-name/
   ├── components/
   │   └── index.ts
   ├── hooks/
   │   └── index.ts
   ├── pages/
   │   └── index.ts
   └── index.ts
   ```

2. **Mỗi module phải export routes từ file index.ts**:
   ```typescript
   // src/modules/module-name/index.ts
   export { default as moduleRoutes } from './routes/moduleRoutes';
   ```

3. **Các component page phải được lazy load**:
   ```typescript
   // src/modules/module-name/routes/moduleRoutes.tsx
   import { lazy } from 'react';

   const ModulePage = lazy(() => import('../pages/ModulePage'));
   ```

## Công cụ và linting

### ESLint Rules

Dự án sử dụng các ESLint rules sau để đảm bảo tuân thủ quy tắc import:

```json
{
  "rules": {
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          ["parent", "sibling"],
          "index",
          "object",
          "type"
        ],
        "pathGroups": [
          {
            "pattern": "react",
            "group": "builtin",
            "position": "before"
          },
          {
            "pattern": "@/**",
            "group": "internal",
            "position": "after"
          }
        ],
        "pathGroupsExcludedImportTypes": ["react"],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "import/no-duplicates": "error",
    "import/no-unresolved": "error",
    "import/no-cycle": "error"
  }
}
```

### Import Sorter

Sử dụng extension VSCode "Import Sorter" để tự động sắp xếp imports theo quy tắc:

```json
{
  "importSorter.generalConfiguration.sortOnBeforeSave": true,
  "importSorter.importStringConfiguration.maximumNumberOfImportExpressionsPerLine.type": "newLineEachExpressionAfterCountLimitExceptIfOnlyOne",
  "importSorter.importStringConfiguration.maximumNumberOfImportExpressionsPerLine.count": 80,
  "importSorter.importStringConfiguration.tabSize": 2,
  "importSorter.importStringConfiguration.quoteMark": "single"
}
```

## Kết luận

Tuân thủ các quy tắc import và export này sẽ giúp code dễ đọc, dễ bảo trì và nhất quán trong toàn bộ dự án. Các quy tắc này không chỉ là hướng dẫn mà còn là tiêu chuẩn bắt buộc trong dự án.

Nếu có bất kỳ thắc mắc hoặc đề xuất nào, vui lòng liên hệ với team lead.
