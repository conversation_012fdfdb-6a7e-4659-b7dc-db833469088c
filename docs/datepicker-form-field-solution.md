# Giải Pháp Lỗi "Expected string, received date" với DatePicker

## Vấn Đề

Khi sử dụng `DatePicker` component với Form validation, thường gặp lỗi:
```
Expected string, received date
```

### Nguyên Nhân
- **DatePicker component** tr<PERSON> về `Date` object khi người dùng chọn ngày
- **Schema validation** trong forms expect `string` 
- **Mismatch** giữa kiểu dữ liệu mà DatePicker trả về và kiểu dữ liệu mà schema expect

## Giải Pháp

### 1. Sử dụng DatePickerFormField

Thay vì sử dụng `DatePicker` trực tiếp, sử dụng `DatePickerFormField`:

```tsx
// ❌ Cách cũ (gây lỗi)
import { DatePicker } from '@/shared/components/common';

<FormItem name="dateOfBirth" label="Ngày sinh" required>
  <DatePicker
    placeholder="Chọn ngày sinh"
    format="dd/MM/yyyy"
    maxDate={new Date()}
  />
</FormItem>

// ✅ Cách mới (đúng)
import { DatePickerFormField } from '@/shared/components/common';

<FormItem name="dateOfBirth" label="Ngày sinh" required>
  <DatePickerFormField
    placeholder="Chọn ngày sinh"
    format="dd/MM/yyyy"
    maxDate={new Date()}
  />
</FormItem>
```

### 2. Sử dụng flexibleDate Schema

Thay vì sử dụng `z.string()`, sử dụng `ValidationSchemas.flexibleDate()`:

```tsx
// ❌ Cách cũ (gây lỗi)
import { z } from 'zod';

const schema = z.object({
  dateOfBirth: z
    .string()
    .min(1, 'Ngày sinh là bắt buộc')
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18 && age <= 100;
    }, 'Tuổi phải từ 18 đến 100'),
});

// ✅ Cách mới (đúng)
import { ValidationSchemas } from '@/shared/validation/schemas';

const schema = z.object({
  dateOfBirth: ValidationSchemas.flexibleDate({ t })
    .refine((date) => {
      const birthDate = typeof date === 'string' ? new Date(date) : date;
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18 && age <= 100;
    }, 'Tuổi phải từ 18 đến 100'),
});
```

## Ví Dụ Hoàn Chỉnh

```tsx
import React, { useRef } from 'react';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';
import { 
  DatePickerFormField, 
  Form, 
  FormItem, 
  Button 
} from '@/shared/components/common';
import { ValidationSchemas } from '@/shared/validation/schemas';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FieldValues } from 'react-hook-form';

const PersonalInfoForm: React.FC = () => {
  const { t } = useTranslation();
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Schema validation với flexibleDate
  const schema = z.object({
    dateOfBirth: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const birthDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        return age >= 18 && age <= 100;
      }, 'Tuổi phải từ 18 đến 100'),
    
    idIssuedDate: ValidationSchemas.flexibleDate({ t })
      .refine((date) => {
        const issuedDate = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        return issuedDate <= today;
      }, 'Ngày cấp phải là quá khứ'),
  });

  const handleSubmit = (data: FieldValues) => {
    console.log('Form data:', data);
    // data.dateOfBirth sẽ là string format "YYYY-MM-DD"
    // data.idIssuedDate sẽ là string format "YYYY-MM-DD"
  };

  return (
    <Form
      ref={formRef}
      schema={schema}
      onSubmit={handleSubmit}
      defaultValues={{
        dateOfBirth: '',
        idIssuedDate: '',
      }}
    >
      <FormItem name="dateOfBirth" label="Ngày sinh" required>
        <DatePickerFormField
          placeholder="Chọn ngày sinh"
          format="dd/MM/yyyy"
          maxDate={new Date()}
        />
      </FormItem>

      <FormItem name="idIssuedDate" label="Ngày cấp" required>
        <DatePickerFormField
          placeholder="Chọn ngày cấp"
          format="dd/MM/yyyy"
          maxDate={new Date()}
        />
      </FormItem>

      <Button type="submit" variant="primary">
        Submit
      </Button>
    </Form>
  );
};
```

## Các Loại Validation Thường Dùng

### 1. Ngày sinh (18-100 tuổi)
```tsx
dateOfBirth: ValidationSchemas.flexibleDate({ t })
  .refine((date) => {
    const birthDate = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    return age >= 18 && age <= 100;
  }, 'Tuổi phải từ 18 đến 100'),
```

### 2. Ngày hẹn (từ hôm nay trở đi)
```tsx
appointmentDate: ValidationSchemas.flexibleDate({ t })
  .refine((date) => {
    const appointmentDate = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return appointmentDate >= today;
  }, 'Ngày hẹn phải từ hôm nay trở đi'),
```

### 3. Ngày kết thúc (sau ngày bắt đầu)
```tsx
endDate: ValidationSchemas.flexibleDate({ t })
  .refine((date, ctx) => {
    if (!date) return true; // Optional field
    const endDate = typeof date === 'string' ? new Date(date) : date;
    const startDate = ctx.parent.startDate;
    if (!startDate) return true;
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    return endDate >= start;
  }, 'Ngày kết thúc phải sau ngày bắt đầu')
```

## Utility Functions

Sử dụng các utility functions từ `@/shared/utils/date-form-utils`:

```tsx
import {
  dateToString,
  stringToDate,
  normalizeDate,
  calculateAge,
  isValidDate,
  isPastDate,
  isFutureDate
} from '@/shared/utils/date-form-utils';

// Chuyển đổi Date thành string
const dateString = dateToString(new Date()); // "2024-01-15"

// Chuyển đổi string thành Date
const date = stringToDate("2024-01-15"); // Date object

// Tính tuổi
const age = calculateAge("1990-01-15"); // 34

// Kiểm tra ngày hợp lệ
const isValid = isValidDate("2024-01-15"); // true
```

## Demo Page

Xem demo tại: `/components/datepicker-form-field`

## Tóm Tắt

1. **Sử dụng `DatePickerFormField`** thay vì `DatePicker` trong forms
2. **Sử dụng `ValidationSchemas.flexibleDate()`** thay vì `z.string()`
3. **Xử lý type checking** trong validation với `typeof date === 'string' ? new Date(date) : date`
4. **DatePickerFormField trả về string** format "YYYY-MM-DD" tương thích với schema
5. **Sử dụng utility functions** để xử lý date operations

Giải pháp này đảm bảo tương thích hoàn toàn giữa DatePicker và Form validation, loại bỏ lỗi "Expected string, received date".
