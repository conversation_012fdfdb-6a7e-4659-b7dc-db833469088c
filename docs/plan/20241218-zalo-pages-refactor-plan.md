# Kế hoạch sửa đổi và phát triển các trang Zalo Marketing

## Tổng quan

Kế hoạch này nhằm sửa đổi và cải thiện các trang trong thư mục `src/modules/marketing/pages/zalo` để sử dụng các component chung của hệ thống, theme, và đa ngôn ngữ một cách nhất quán.

## Phân tích hiện trạng

### Các trang hiện tại:
1. **ZaloAccountsPage.tsx** - Quản lý Zalo OA Accounts
2. **ZaloFollowersPage.tsx** - Quản lý Followers
3. **ZaloOverviewPage.tsx** - Trang tổng quan Zalo Marketing
4. **ZaloZnsPage.tsx** - Quản lý ZNS Templates

### Vấn đề hiện tại:
- Sử dụng table tự tạo thay vì component Table chung
- Không sử dụng MenuIconBar, ActiveFilters
- Không sử dụng useDataTable, useDataTableConfig hooks
- Pagination component riêng lẻ thay vì tích hợp trong Table
- Thiếu tính nhất quán về theme và màu sắc
- Chưa tối ưu đa ngôn ngữ

## Mục tiêu

1. **Sử dụng component chung**: Table, MenuIconBar, ActiveFilters, Card, Button, etc.
2. **Tích hợp hooks chung**: useDataTable, useDataTableConfig, useActiveFilters
3. **Chuẩn hóa theme**: Sử dụng CSS variables, màu sắc nhất quán
4. **Tối ưu đa ngôn ngữ**: Bổ sung key dịch cho Zalo module
5. **Cải thiện UX**: Loading states, error handling, responsive design

## Kế hoạch thực hiện

### Phase 1: Chuẩn bị cơ sở hạ tầng

#### 1.1 Bổ sung file ngôn ngữ cho Zalo
**File cần tạo/sửa:**
- `src/modules/marketing/locales/vi.json` - Bổ sung section zalo
- `src/modules/marketing/locales/en.json` - Bổ sung section zalo  
- `src/modules/marketing/locales/zh.json` - Bổ sung section zalo

**Cấu trúc key dịch:**
```json
{
  "marketing": {
    "zalo": {
      "overview": {
        "title": "Zalo Marketing",
        "description": "Quản lý Zalo Official Account và chiến dịch ZNS",
        "connectAccount": "Kết nối OA",
        "stats": {
          "totalAccounts": "Tổng số OA",
          "totalFollowers": "Tổng Followers",
          "messagesSent": "Tin nhắn đã gửi",
          "engagementRate": "Tỷ lệ tương tác"
        }
      },
      "accounts": {
        "title": "Quản lý Zalo OA",
        "description": "Kết nối và quản lý các Zalo Official Account",
        "connectNew": "Kết nối OA mới",
        "table": {
          "name": "Tên OA",
          "oaId": "OA ID", 
          "followers": "Followers",
          "status": "Trạng thái",
          "lastUpdate": "Cập nhật cuối",
          "actions": "Thao tác"
        }
      },
      "followers": {
        "title": "Quản lý Followers",
        "description": "Quản lý danh sách followers",
        "export": "Export",
        "sync": "Đồng bộ"
      },
      "zns": {
        "title": "ZNS Templates",
        "description": "Quản lý template thông báo Zalo Notification Service",
        "createTemplate": "Tạo Template"
      }
    }
  }
}
```

#### 1.2 Tạo types và interfaces chung
**File cần tạo/sửa:**
- `src/modules/marketing/types/zalo.types.ts` - Chuẩn hóa types

### Phase 2: Refactor ZaloAccountsPage

#### 2.1 Sử dụng component chung
- Thay thế table tự tạo bằng component `Table` từ `@/shared/components/common`
- Sử dụng `MenuIconBar` cho search, filter, actions
- Sử dụng `ActiveFilters` để hiển thị filter đang áp dụng
- Tích hợp `SlideInForm` cho form kết nối OA

#### 2.2 Tích hợp hooks chung
- Sử dụng `useDataTable` và `useDataTableConfig`
- Sử dụng `useActiveFilters` 
- Sử dụng `useSlideForm` cho form animation

#### 2.3 Cải thiện UX
- Loading states với skeleton
- Error handling
- Responsive design
- Pagination tích hợp trong Table

### Phase 3: Refactor ZaloFollowersPage

#### 3.1 Tương tự ZaloAccountsPage
- Sử dụng component Table chung
- MenuIconBar với bulk actions
- ActiveFilters
- Stats cards với theme nhất quán

#### 3.2 Tính năng đặc biệt
- Bulk selection và actions
- Export functionality
- Real-time sync

### Phase 4: Refactor ZaloZnsPage

#### 4.1 Table component
- Thay thế table HTML bằng component Table
- Column configuration
- Sorting và filtering

#### 4.2 Modal và forms
- Sử dụng Modal component chung
- Form validation
- Preview functionality

### Phase 5: Refactor ZaloOverviewPage

#### 5.1 Stats cards
- Sử dụng Card component chung
- Theme colors nhất quán
- Loading states

#### 5.2 Account list
- Component Table cho danh sách accounts
- Action buttons nhất quán

### Phase 6: Tối ưu theme và styling

#### 6.1 CSS Variables
- Sử dụng theme colors từ CSS variables
- Dark mode support
- Gradient colors (#FF3333, #FFCC99)

#### 6.2 Component styling
- Box shadow thay vì borders
- Consistent spacing
- Responsive breakpoints

## Chi tiết kỹ thuật

### Component Table Configuration
```typescript
const columns = useMemo<TableColumn<ZaloOAAccountDto>[]>(() => [
  {
    key: 'name',
    title: t('marketing:zalo.accounts.table.name'),
    dataIndex: 'name',
    sortable: true,
  },
  {
    key: 'oaId', 
    title: t('marketing:zalo.accounts.table.oaId'),
    dataIndex: 'oaId',
    sortable: true,
  },
  // ... more columns
], [t]);
```

### useDataTable Integration
```typescript
const dataTable = useDataTable(
  useDataTableConfig<ZaloOAAccountDto, ZaloOAAccountQueryDto>({
    columns,
    filterOptions,
    showDateFilter: false,
    createQueryParams: (params) => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    }),
  })
);
```

### MenuIconBar Usage
```typescript
<MenuIconBar
  onSearch={dataTable.tableData.handleSearch}
  onAdd={() => showForm()}
  items={dataTable.menuItems}
  onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
  columns={dataTable.columnVisibility.visibleColumns}
  showDateFilter={false}
  showColumnFilter={true}
/>
```

## Timeline

- **Week 1**: Phase 1 - Chuẩn bị cơ sở hạ tầng
- **Week 2**: Phase 2 - Refactor ZaloAccountsPage  
- **Week 3**: Phase 3 - Refactor ZaloFollowersPage
- **Week 4**: Phase 4 - Refactor ZaloZnsPage
- **Week 5**: Phase 5 - Refactor ZaloOverviewPage
- **Week 6**: Phase 6 - Tối ưu theme và testing

## Checklist

### Cho mỗi trang:
- [ ] Sử dụng component Table chung
- [ ] Tích hợp MenuIconBar
- [ ] Sử dụng ActiveFilters
- [ ] Hooks useDataTable, useActiveFilters
- [ ] Đa ngôn ngữ đầy đủ
- [ ] Theme colors nhất quán
- [ ] Loading states
- [ ] Error handling
- [ ] Responsive design
- [ ] TypeScript strict compliance

## Lưu ý quan trọng

1. **Backward compatibility**: Đảm bảo API calls không thay đổi
2. **Performance**: Sử dụng useMemo, useCallback appropriately
3. **Accessibility**: ARIA labels, keyboard navigation
4. **Testing**: Unit tests cho các component mới
5. **Documentation**: Cập nhật README và comments

## Kết quả mong đợi

Sau khi hoàn thành:
- Code consistency cao
- Maintainability tốt hơn
- User experience cải thiện
- Performance tối ưu
- Đa ngôn ngữ hoàn chỉnh
- Theme system nhất quán
