import { QueryDto, SortDirection } from '@/common/dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

/**
 * Enum cho các trường sắp xếp sản phẩm
 */
export enum ProductSortField {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  LISTED_PRICE = 'listedPrice',
  DISCOUNTED_PRICE = 'discountedPrice',
}

/**
 * DTO cho các tham số truy vấn danh sách sản phẩm
 */
export class QueryProductDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.AGENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductCategory)
  @Type(() => String)
  category?: ProductCategory;

  @ApiProperty({
    description: 'Lọc theo trạng thái sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductStatus)
  @Type(() => String)
  status?: ProductStatus;

  @ApiProperty({
    description: 'Lọc theo ID người dùng',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: 'Lọc theo ID nhân viên',
    example: 456,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  employeeId?: number;

  @ApiProperty({
    description: 'Bao gồm cả sản phẩm đã bị xóa',
    example: false,
    required: false,
    default: false
  })
  @IsOptional()
  @Type(() => Boolean)
  includeDeleted?: boolean = false;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ProductSortField,
    default: ProductSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductSortField)
  sortBy?: ProductSortField = ProductSortField.CREATED_AT;

  constructor() {
    super();
    this.sortBy = ProductSortField.CREATED_AT;
    this.sortDirection = SortDirection.DESC;
  }
}
