import { ApiProperty } from '@nestjs/swagger';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

/**
 * DTO cho thông tin người bán trong response sản phẩm
 */
export class SellerInfoDto {
  @ApiProperty({
    description: 'ID của người bán',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên người bán',
    example: 'Nguyễn <PERSON>n <PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Avatar của người bán',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> ngườ<PERSON> bán (user hoặc employee)',
    example: 'user',
    enum: ['user', 'employee'],
  })
  type: 'user' | 'employee';
}

/**
 * DTO cho response trả về thông tin sản phẩm trong danh sách
 */
export class ProductResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'A ready-to-use chatbot template for customer service',
  })
  description: string;

  @ApiProperty({
    description: 'Giá niêm yết',
    example: 1200,
  })
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.AGENT,
  })
  category: ProductCategory;

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
  })
  status: ProductStatus;

  @ApiProperty({
    description: 'Danh sách ảnh sản phẩm',
    type: [String],
    example: ['https://example.com/image1.jpg'],
  })
  images: string[];

  @ApiProperty({
    description: 'Thông tin người bán',
    type: SellerInfoDto,
  })
  seller: SellerInfoDto;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1625184000000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Tổng số lượng đã bán',
    example: 25,
  })
  soldCount: number;

  @ApiProperty({
    description: 'Có thể mua sản phẩm này không',
    example: true,
  })
  canPurchase: boolean;
}
