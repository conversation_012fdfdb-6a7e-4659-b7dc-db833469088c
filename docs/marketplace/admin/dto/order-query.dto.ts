import { QueryDto, SortDirection } from '@/common/dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';

/**
 * Enum cho các trường sắp xếp đơn hàng
 */
export enum OrderSortField {
  CREATED_AT = 'createdAt',
  TOTAL_AMOUNT = 'totalAmount',
}

/**
 * DTO cho các tham số truy vấn danh sách đơn hàng
 */
export class OrderQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo ID người dùng',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: OrderSortField,
    default: OrderSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderSortField)
  sortBy?: OrderSortField = OrderSortField.CREATED_AT;

  constructor() {
    super();
    this.sortBy = OrderSortField.CREATED_AT;
    this.sortDirection = SortDirection.DESC;
  }
}
