import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateProductStatusDto } from '../../dto/update-product-status.dto';
import { ProductStatus } from '@modules/marketplace/enums';

describe('UpdateProductStatusDto', () => {
  it('phải xác thực DTO hợp lệ với trạng thái DRAFT', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      status: ProductStatus.DRAFT,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ với trạng thái PENDING', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      status: ProductStatus.PENDING,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ với trạng thái APPROVED', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      status: ProductStatus.APPROVED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ với trạng thái REJECTED', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      status: ProductStatus.REJECTED,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải thất bại khi thiếu trạng thái', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải thất bại khi trạng thái không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(UpdateProductStatusDto, {
      status: 'INVALID_STATUS', // Trạng thái không hợp lệ
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });
});
