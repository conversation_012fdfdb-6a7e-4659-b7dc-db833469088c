import { Test, TestingModule } from '@nestjs/testing';
import { CartAdminService } from '@modules/marketplace/admin/services/cart-admin.service';
import { CartRepository, CartItemRepository } from '@modules/marketplace/repositories';
import { CartHelper } from '@modules/marketplace/helpers';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { mockCart, mockCarts, mockCartResponseDto, mockPaginatedCartResponseDto } from '../__mocks__/cart.mock';
import { CartQueryDto } from '@modules/marketplace/admin/dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';

describe('Dịch vụ quản lý giỏ hàng (Admin)', () => {
  let service: CartAdminService;
  let cartRepository: CartRepository;
  let cartItemRepository: CartItemRepository;
  let userRepository: UserRepository;
  let cartHelper: CartHelper;

  const mockCartRepository = {
    findById: jest.fn(),
    findAdminCarts: jest.fn(),
  };

  const mockCartItemRepository = {
    findApprovedItemsByCartId: jest.fn(),
  };

  const mockUserRepository = {
    findById: jest.fn(),
  };

  const mockCartHelper = {
    mapToCartAdminResponseDto: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CartAdminService,
        {
          provide: CartRepository,
          useValue: mockCartRepository,
        },
        {
          provide: CartItemRepository,
          useValue: mockCartItemRepository,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: CartHelper,
          useValue: mockCartHelper,
        },
      ],
    }).compile();

    service = module.get<CartAdminService>(CartAdminService);
    cartRepository = module.get<CartRepository>(CartRepository);
    cartItemRepository = module.get<CartItemRepository>(CartItemRepository);
    userRepository = module.get<UserRepository>(UserRepository);
    cartHelper = module.get<CartHelper>(CartHelper);
  });

  it('phải được định nghĩa', () => {
    expect(service).toBeDefined();
  });

  describe('lấy danh sách giỏ hàng', () => {
    it('phải trả về danh sách giỏ hàng có phân trang', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new CartQueryDto();

      const mockPaginatedResult: PaginatedResult<any> = {
        items: mockCarts,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(cartRepository, 'findAdminCarts').mockResolvedValue(mockPaginatedResult);
      jest.spyOn(cartHelper, 'mapToCartAdminResponseDto').mockResolvedValue(mockCartResponseDto);

      // Act
      const result = await service.getCarts(employeeId, queryDto);

      // Assert
      expect(cartRepository.findAdminCarts).toHaveBeenCalledWith(queryDto);
      expect(cartHelper.mapToCartAdminResponseDto).toHaveBeenCalledTimes(mockCarts.length);
      expect(result.items.length).toBe(mockCarts.length);
      expect(result.meta).toEqual(mockPaginatedResult.meta);
    });

    it('phải trả về mảng rỗng khi không tìm thấy giỏ hàng nào', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new CartQueryDto();

      const emptyPaginatedResult: PaginatedResult<any> = {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
      };

      jest.spyOn(cartRepository, 'findAdminCarts').mockResolvedValue(emptyPaginatedResult);

      // Act
      const result = await service.getCarts(employeeId, queryDto);

      // Assert
      expect(cartRepository.findAdminCarts).toHaveBeenCalledWith(queryDto);
      expect(result.items).toEqual([]);
      expect(result.meta).toEqual(emptyPaginatedResult.meta);
    });

    it('phải xử lý lỗi trong quá trình chuyển đổi dữ liệu giỏ hàng', async () => {
      // Arrange
      const employeeId = 1;
      const queryDto = new CartQueryDto();

      const mockPaginatedResult: PaginatedResult<any> = {
        items: mockCarts,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(cartRepository, 'findAdminCarts').mockResolvedValue(mockPaginatedResult);

      // First call succeeds, second call throws error
      jest.spyOn(cartHelper, 'mapToCartAdminResponseDto')
        .mockResolvedValueOnce(mockCartResponseDto)
        .mockImplementationOnce(() => {
          throw new Error('Mapping error');
        });

      // Act & Assert
      try {
        await service.getCarts(employeeId, queryDto);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.CART_RETRIEVAL_FAILED);
        expect(error.message).toContain('Lỗi khi chuyển đổi giỏ hàng');
      }
    });
  });

  describe('lấy thông tin chi tiết giỏ hàng theo ID', () => {
    it('phải trả về thông tin chi tiết giỏ hàng theo ID', async () => {
      // Arrange
      const employeeId = 1;
      const cartId = 1;

      jest.spyOn(cartRepository, 'findById').mockResolvedValue(mockCart);
      jest.spyOn(cartHelper, 'mapToCartAdminResponseDto').mockResolvedValue(mockCartResponseDto);

      // Act
      const result = await service.getCartById(employeeId, cartId);

      // Assert
      expect(cartRepository.findById).toHaveBeenCalledWith(cartId);
      expect(cartHelper.mapToCartAdminResponseDto).toHaveBeenCalledWith(mockCart);
      expect(result).toEqual(mockCartResponseDto);
    });

    it('phải ném AppException khi không tìm thấy giỏ hàng', async () => {
      // Arrange
      const employeeId = 1;
      const cartId = 999;

      jest.spyOn(cartRepository, 'findById').mockResolvedValue(null);

      // Act & Assert
      try {
        await service.getCartById(employeeId, cartId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.CART_NOT_FOUND);
      }
    });

    it('phải xử lý và ném lại AppException', async () => {
      // Arrange
      const employeeId = 1;
      const cartId = 1;

      const appException = new AppException(
        MARKETPLACE_ERROR_CODES.CART_NOT_FOUND,
        'Cart not found'
      );

      jest.spyOn(cartRepository, 'findById').mockRejectedValue(appException);

      // Act & Assert
      try {
        await service.getCartById(employeeId, cartId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.CART_NOT_FOUND);
      }
    });

    it('phải bọc các lỗi khác trong AppException', async () => {
      // Arrange
      const employeeId = 1;
      const cartId = 1;

      jest.spyOn(cartRepository, 'findById').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      try {
        await service.getCartById(employeeId, cartId);
        fail('Expected AppException to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(AppException);
        expect(error.errorCode).toBe(MARKETPLACE_ERROR_CODES.GENERAL_ERROR);
      }
    });
  });
});
