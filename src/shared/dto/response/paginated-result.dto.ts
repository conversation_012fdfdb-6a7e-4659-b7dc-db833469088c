/**
 * DTO cho kết quả phân trang
 */
export interface PaginationMeta {
  /**
   * Tổng số items
   */
  totalItems: number;

  /**
   * Số lượng items trong trang hiện tại
   */
  itemCount: number;

  /**
   * Số lượng items trên mỗi trang
   */
  itemsPerPage: number;

  /**
   * Tổng số trang
   */
  totalPages: number;

  /**
   * Trang hiện tại
   */
  currentPage: number;
}

/**
 * DTO cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  /**
   * Danh sách items
   */
  items: T[];

  /**
   * Thông tin phân trang
   */
  meta: PaginationMeta;
}
