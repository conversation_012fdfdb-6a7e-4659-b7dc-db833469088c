/**
 * Hook tạo các tùy chọn lọc phổ biến
 * @module useCommonFilterOptions
 */
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FilterOption } from './useFilterOptions';

/**
 * Tạo tùy chọn lọc trạng thái hoạt động/không hoạt động
 * @param activeValue Giá trị cho trạng thái hoạt động
 * @param inactiveValue Giá trị cho trạng thái không hoạt động
 * @returns Danh sách tùy chọn lọc
 */
export function useStatusFilterOptions<T extends string | number>(
  activeValue: T,
  inactiveValue: T
): FilterOption[] {
  const { t } = useTranslation(['common']);

  return useMemo(
    () => [
      {
        id: 'all',
        label: t('common:all'),
        icon: 'list',
        value: 'all',
      },
      {
        id: 'active',
        label: t('common:active'),
        icon: 'check',
        value: activeValue,
      },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: inactiveValue,
      },
    ],
    [t, activeValue, inactiveValue]
  );
}

/**
 * Tạo tùy chọn lọc boolean (có/không)
 * @returns Danh sách tùy chọn lọc
 */
export function useBooleanFilterOptions(): FilterOption[] {
  const { t } = useTranslation(['common']);

  return useMemo(
    () => [
      {
        id: 'all',
        label: t('common:all'),
        icon: 'list',
        value: 'all',
      },
      {
        id: 'yes',
        label: t('common:yes'),
        icon: 'check',
        value: true,
      },
      {
        id: 'no',
        label: t('common:no'),
        icon: 'x',
        value: false,
      },
    ],
    [t]
  );
}
