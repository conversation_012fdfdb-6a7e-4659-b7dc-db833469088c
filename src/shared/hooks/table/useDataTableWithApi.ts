/**
 * Hook tổng hợp quản lý dữ liệu bảng với tích hợp API
 * @module useDataTableWithApi
 */
import { useEffect } from 'react';
import { useDataTable, UseDataTableOptions, PaginatedData } from './useDataTable';

/**
 * Interface cho tham số đầu vào của hook useDataTableWithApi
 */
export interface UseDataTableWithApiOptions<T, TQueryParams>
  extends Omit<UseDataTableOptions<T, TQueryParams>, 'apiData' | 'isLoading'> {
  /**
   * Hàm gọi API
   */
  apiCallback: (params: TQueryParams) => {
    data: PaginatedData<T> | undefined;
    isLoading: boolean;
  };
}

/**
 * Hook tổng hợp quản lý dữ liệu bảng với tích hợp API
 * @template T Kiểu dữ liệu của dòng trong bảng
 * @template TQueryParams Kiểu dữ liệu của query params
 * @param options Tùy chọn cho hook
 * @returns Các state và hàm xử lý cho bảng
 */
export function useDataTableWithApi<T, TQueryParams>(
  options: UseDataTableWithApiOptions<T, TQueryParams>
) {
  const { apiCallback, ...restOptions } = options;

  // Sử dụng hook useDataTable
  const dataTable = useDataTable<T, TQueryParams>(restOptions);

  // Gọi API với queryParams từ dataTable
  const { data, isLoading } = apiCallback(dataTable.queryParams);

  // Tự động cập nhật dữ liệu khi data hoặc isLoading thay đổi
  useEffect(() => {
    dataTable.updateTableData(data, isLoading);
  }, [data, isLoading, dataTable]);

  return dataTable;
}
