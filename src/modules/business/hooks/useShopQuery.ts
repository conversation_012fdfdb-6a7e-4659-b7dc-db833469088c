import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';
import { ShopService } from '../services/shop.service';
import { CreateOrUpdateShopInfoDto } from '../types/shop.types';

/**
 * Query keys cho shop
 */
export const SHOP_QUERY_KEYS = {
  all: ['shop'] as const,
  list: () => [...SHOP_QUERY_KEYS.all, 'list'] as const,
  info: () => [...SHOP_QUERY_KEYS.all, 'info'] as const,
  detail: (id: string) => [...SHOP_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook để lấy danh sách cửa hàng
 */
export const useShopList = () => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.list(),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopList();
        console.log('🔍 [useShopList] API response:', response);
        return response.data || response.result || [];
      } catch (error) {
        console.error('🔍 [useShopList] Error:', error);
        throw error;
      }
    },
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (chưa có cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để lấy thông tin cửa hàng đơn lẻ (giữ lại để tương thích)
 */
export const useShopInfo = () => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.info(),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopInfo();
        console.log('🔍 [useShopInfo] API response:', response);
        return response.data || response.result;
      } catch (error) {
        console.error('🔍 [useShopInfo] Error:', error);
        throw error;
      }
    },
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (chưa có thông tin cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để lấy thông tin chi tiết cửa hàng theo ID
 */
export const useShopDetail = (id: string) => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.detail(id),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopDetail(id);
        console.log('🔍 [useShopDetail] API response:', response);
        return response.data || response.result;
      } catch (error) {
        console.error('🔍 [useShopDetail] Error:', error);
        throw error;
      }
    },
    enabled: !!id, // Chỉ chạy query khi có ID
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (không tìm thấy cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để tạo thông tin cửa hàng
 */
export const useCreateShopInfo = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrUpdateShopInfoDto) => ShopService.createShopInfo(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:shop.messages.createSuccess'),
      });
      // Invalidate và refetch shop list và info
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.info() });
    },
    onError: (error: Error) => {
      console.error('Create shop info error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.createError'),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin cửa hàng
 */
export const useUpdateShopInfo = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: CreateOrUpdateShopInfoDto }) =>
      ShopService.updateShopInfo(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:shop.messages.updateSuccess'),
      });
      // Invalidate và refetch shop list, info và detail
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.info() });
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.detail(variables.id) });
    },
    onError: (error: Error) => {
      console.error('Update shop info error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.updateError'),
      });
    },
  });
};
