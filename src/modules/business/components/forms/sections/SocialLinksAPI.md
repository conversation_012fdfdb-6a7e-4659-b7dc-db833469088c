# Social Links API Integration - Customer Detail

## Tổng quan

Đã đấu nối API cho phần mạng xã hội trong customer detail với endpoint:
`PUT /v1/user/convert-customers/{id}/social-links`

## API Integration

### Endpoint
```
PUT /v1/user/convert-customers/{id}/social-links
```

### Request Format
```json
{
  "facebookLink": "https://facebook.com/user123",
  "twitterLink": "https://twitter.com/user123", 
  "linkedinLink": "https://linkedin.com/in/user123",
  "zaloLink": "https://zalo.me/user123",
  "websiteLink": "https://website.com"
}
```

### Response Format
```json
{
  "code": 200,
  "message": "Cập nhật liên kết mạng xã hội thành công",
  "result": {
    // Customer data with updated social links
  }
}
```

## Component Implementation

### CustomerSocial Component
- **File**: `src/modules/business/components/forms/sections/CustomerSocial.tsx`
- **Hook**: `useUpdateCustomerSocialLinks`
- **Validation**: `createUpdateCustomerSocialLinksSchema`

### Features Implemented

#### 1. **Form State Management**
```typescript
const [formData, setFormData] = useState({
  facebook: customer.socialProfiles?.facebook || '',
  twitter: customer.socialProfiles?.twitter || '',
  linkedin: customer.socialProfiles?.linkedin || '',
  zalo: customer.socialProfiles?.zalo || '',
  website: customer.socialProfiles?.website || '',
});
```

#### 2. **API Integration**
```typescript
const updateSocialLinksMutation = useUpdateCustomerSocialLinks();

const handleSave = async () => {
  // Map form data to API format
  const socialLinksData = {
    facebookLink: formData.facebook || '',
    twitterLink: formData.twitter || '',
    linkedinLink: formData.linkedin || '',
    zaloLink: formData.zalo || '',
    websiteLink: formData.website || '',
  };

  // Validate and call API
  const validatedData = socialLinksSchema.parse(socialLinksData);
  const cleanedData = Object.fromEntries(
    Object.entries(validatedData).filter(([_, value]) => value !== '')
  );

  await updateSocialLinksMutation.mutateAsync({
    id: parseInt(customer.id),
    data: cleanedData,
  });
};
```

#### 3. **Validation Schema**
```typescript
export const createUpdateCustomerSocialLinksSchema = (t: TFunction) => z.object({
  facebookLink: z.string().url(t('validation:url')).optional().or(z.literal('')),
  twitterLink: z.string().url(t('validation:url')).optional().or(z.literal('')),
  linkedinLink: z.string().url(t('validation:url')).optional().or(z.literal('')),
  zaloLink: z.string().optional().or(z.literal('')), // Zalo có thể là số điện thoại
  websiteLink: z.string().url(t('validation:url')).optional().or(z.literal(''))
});
```

#### 4. **UI Features**
- ✅ **Form inputs** cho từng platform
- ✅ **Platform icons** với màu sắc riêng biệt
- ✅ **Loading state** khi đang save
- ✅ **Error handling** với notifications
- ✅ **Success notifications** khi save thành công

#### 5. **Platform Support**
- 📘 **Facebook**: URL validation
- 🐦 **Twitter**: URL validation  
- 💼 **LinkedIn**: URL validation
- 💬 **Zalo**: Flexible (phone number or URL)
- 🌐 **Website**: URL validation

## Hook Implementation

### useUpdateCustomerSocialLinks
```typescript
export const useUpdateCustomerSocialLinks = (options?: {
  showSuccessNotification?: boolean;
  showErrorNotification?: boolean;
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomerSocialLinksDto }) =>
      CustomerService.updateCustomerSocialLinks(id, data),
    onSuccess: (_, variables) => {
      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.all });
      queryClient.invalidateQueries({ queryKey: CUSTOMER_QUERY_KEYS.detail(variables.id) });
      
      // Success notification
      NotificationUtil.success({
        message: 'Cập nhật liên kết mạng xã hội thành công'
      });
    },
    onError: (error: AxiosError) => {
      // Error notification
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi cập nhật liên kết mạng xã hội'
      });
    },
  });
};
```

## Service Implementation

### CustomerService.updateCustomerSocialLinks
```typescript
updateCustomerSocialLinks: async (
  id: number,
  data: UpdateCustomerSocialLinksDto
): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
  return apiClient.put(`/user/convert-customers/${id}/social-links`, data);
}
```

## Data Flow

1. **User Input** → Form fields cho từng platform
2. **Validation** → Zod schema validation với URL checking
3. **Data Mapping** → Convert form data sang API format
4. **API Call** → PUT request với social links data
5. **Success Handling** → Notifications + query invalidation
6. **Error Handling** → Validation errors + API errors

## Testing

### Test Cases

1. **✅ Valid URLs**
   - Nhập URLs hợp lệ cho Facebook, Twitter, LinkedIn, Website
   - Click Save → API call thành công

2. **✅ Invalid URLs**
   - Nhập URLs không hợp lệ
   - Click Save → Validation error notification

3. **✅ Zalo Flexibility**
   - Nhập số điện thoại cho Zalo → Thành công
   - Nhập URL cho Zalo → Thành công

4. **✅ Empty Fields**
   - Để trống một số fields → Chỉ gửi fields có giá trị
   - Empty fields không được gửi trong API call

5. **✅ Loading States**
   - Click Save → Button hiển thị "Đang lưu..." và disabled
   - API complete → Button trở về bình thường

## Benefits

1. **🔗 Complete Integration**: Đầy đủ CRUD cho social links
2. **✅ Validation**: URL validation cho tất cả platforms
3. **🎯 Flexible**: Zalo support cả phone và URL
4. **📱 User-Friendly**: Clear icons và placeholders
5. **🔔 Notifications**: Success/error feedback
6. **⚡ Performance**: Query invalidation để sync data

## Notes

- Zalo field linh hoạt, có thể nhập số điện thoại hoặc URL
- Empty fields không được gửi trong API request
- Validation errors hiển thị qua notifications
- Query invalidation đảm bảo data consistency
- Component giữ nguyên giao diện như thiết kế ban đầu
