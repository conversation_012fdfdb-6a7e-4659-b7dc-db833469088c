import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  ShopInfoDto,
  CreateOrUpdateShopInfoDto,
} from '../types/shop.types';

/**
 * Service xử lý API liên quan đến thông tin cửa hàng
 */
export const ShopService = {
  /**
   * Lấy danh sách cửa hàng
   * @returns Danh sách cửa hàng
   */
  getShopList: async (): Promise<ApiResponseDto<ShopInfoDto[]>> => {
    return apiClient.get('/user/shop-info/all');
  },

  /**
   * Lấy thông tin cửa hàng đơn lẻ (giữ lại để tương thích)
   * @returns Thông tin cửa hàng
   */
  getShopInfo: async (): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.get('/user/shop-info');
  },

  /**
   * L<PERSON>y thông tin chi tiết cửa hàng theo ID
   * @param id ID của cửa hàng
   * @returns Thông tin chi tiết cửa hàng
   */
  getShopDetail: async (id: string): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.get(`/user/shop-info/${id}`);
  },

  /**
   * Tạo thông tin cửa hàng mới
   * @param data Dữ liệu cửa hàng
   * @returns Thông tin cửa hàng được tạo
   */
  createShopInfo: async (
    data: CreateOrUpdateShopInfoDto
  ): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.post('/user/shop-info/create', data);
  },

  /**
   * Cập nhật thông tin cửa hàng theo ID
   * @param id ID của cửa hàng
   * @param data Dữ liệu cập nhật
   * @returns Thông tin cửa hàng được cập nhật
   */
  updateShopInfo: async (
    id: string,
    data: CreateOrUpdateShopInfoDto
  ): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.put(`/user/shop-info/${id}`, data);
  },
};

export default ShopService;
