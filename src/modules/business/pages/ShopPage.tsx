import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Card,
  Typography,
  Form,
  FormItem,
  Input,
  Button,
  Grid,
  Loading,
  Icon,
  SlideInForm,
  Table,
  EmptyState,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useSlideForm } from '@/shared/hooks/useSlideForm';
import { useShopList, useCreateShopInfo, useUpdateShopInfo } from '../hooks/useShopQuery';
import { ShopInfoDto } from '../types/shop.types';
import { formatTimestamp } from '@/shared/utils/date';

// Schema validation cho form
const shopInfoSchema = z.object({
  shopName: z.string().min(1, 'Tên cửa hàng là bắt buộc'),
  shopPhone: z.string().min(1, '<PERSON><PERSON> điện thoại là bắt buộc'),
  shopAddress: z.string().min(1, 'Địa chỉ là bắt buộc'),
  shopProvince: z.string().min(1, 'Tỉnh/Thành phố là bắt buộc'),
  shopDistrict: z.string().min(1, 'Quận/Huyện là bắt buộc'),
  shopWard: z.string().min(1, 'Phường/Xã là bắt buộc'),
});

type ShopInfoFormData = z.infer<typeof shopInfoSchema>;

/**
 * Component form tạo/cập nhật thông tin cửa hàng
 */
const ShopInfoForm: React.FC<{
  shopInfo?: ShopInfoDto;
  onSubmit: (data: ShopInfoFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
}> = ({ shopInfo, onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation('business');

  // Default values cho form
  const defaultValues: ShopInfoFormData = {
    shopName: shopInfo?.shopName || '',
    shopPhone: shopInfo?.shopPhone || '',
    shopAddress: shopInfo?.shopAddress || '',
    shopProvince: shopInfo?.shopProvince || '',
    shopDistrict: shopInfo?.shopDistrict || '',
    shopWard: shopInfo?.shopWard || '',
  };

  return (
    <div className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <Icon name="marketplace" className="text-primary" size="lg" />
        <div>
          <Typography variant="h4" className="mb-1">
            {shopInfo
              ? t('business:shop.form.editTitle', 'Cập nhật thông tin cửa hàng')
              : t('business:shop.form.createTitle', 'Tạo thông tin cửa hàng')
            }
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('business:shop.form.description', 'Nhập thông tin cửa hàng để quản lý đơn hàng và vận chuyển')}
          </Typography>
        </div>
      </div>

      <Form
        schema={shopInfoSchema}
        onSubmit={onSubmit}
        defaultValues={defaultValues}
        key={shopInfo?.id || 'new'}
      >
        <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="md">
          <FormItem name="shopName" label={t('business:shop.form.shopName')} required>
            <Input placeholder={t('business:shop.form.shopNamePlaceholder')} />
          </FormItem>

          <FormItem name="shopPhone" label={t('business:shop.form.shopPhone')} required>
            <Input placeholder={t('business:shop.form.shopPhonePlaceholder')} />
          </FormItem>

          <div className="md:col-span-2">
            <FormItem name="shopAddress" label={t('business:shop.form.shopAddress')} required>
              <Input placeholder={t('business:shop.form.shopAddressPlaceholder')} />
            </FormItem>
          </div>
        </Grid>

        <Grid columns={{ xs: 1, md: 3 }} columnGap="md" rowGap="md" className="mt-4">
          <FormItem name="shopProvince" label={t('business:shop.form.shopProvince')} required>
            <Input placeholder={t('business:shop.form.shopProvincePlaceholder')} />
          </FormItem>

          <FormItem name="shopDistrict" label={t('business:shop.form.shopDistrict')} required>
            <Input placeholder={t('business:shop.form.shopDistrictPlaceholder')} />
          </FormItem>

          <FormItem name="shopWard" label={t('business:shop.form.shopWard')} required>
            <Input placeholder={t('business:shop.form.shopWardPlaceholder')} />
          </FormItem>
        </Grid>

        <div className="flex gap-3 justify-end mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('business:common.actions.cancel')}
          </Button>

          <Button
            type="submit"
            variant="default"
            disabled={isSubmitting}
            loading={isSubmitting}
          >
            {shopInfo
              ? t('business:common.actions.save')
              : t('business:common.actions.create')
            }
          </Button>
        </div>
      </Form>
    </div>
  );
};

/**
 * Trang quản lý thông tin cửa hàng
 */
const ShopPage: React.FC = () => {
  const { t } = useTranslation('business');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingShop, setEditingShop] = useState<ShopInfoDto | undefined>(undefined);

  // Hook quản lý SlideInForm
  const {
    isVisible: isFormVisible,
    showForm,
    hideForm,
  } = useSlideForm();

  // Hooks
  const { data: shopList, isLoading, error } = useShopList();
  const createShopMutation = useCreateShopInfo();
  const updateShopMutation = useUpdateShopInfo();

  // Debug logging
  console.log('🔍 [ShopPage] shopList:', shopList);
  console.log('🔍 [ShopPage] isLoading:', isLoading);
  console.log('🔍 [ShopPage] error:', error);
  console.log('🔍 [ShopPage] editingShop:', editingShop);
  console.log('🔍 [ShopPage] updateShopMutation status:', updateShopMutation.status);
  console.log('🔍 [ShopPage] translations test:', {
    title: t('business:shop.title'),
    description: t('business:shop.description'),
    emptyTitle: t('business:shop.empty.title'),
  });

  // Xử lý chỉnh sửa cửa hàng
  const handleEditShop = useCallback((shop: ShopInfoDto) => {
    setEditingShop(shop);
    showForm();
  }, [showForm]);

  // Cấu hình columns cho table
  const columns = useMemo(() => [
    {
      title: t('business:shop.form.shopName'),
      dataIndex: 'shopName',
      key: 'shopName',
    },
    {
      title: t('business:shop.form.shopPhone'),
      dataIndex: 'shopPhone',
      key: 'shopPhone',
    },
    {
      title: t('business:shop.form.shopAddress'),
      dataIndex: 'shopAddress',
      key: 'shopAddress',
    },
    {
      title: t('business:shop.form.shopProvince'),
      dataIndex: 'shopProvince',
      key: 'shopProvince',
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (value: unknown) => formatTimestamp(Number(value)),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 100,
      render: (_, record: ShopInfoDto) => (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleEditShop(record)}
            title={t('common:edit')}
            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
          >
            <Icon name="edit" size="sm" />
          </Button>
        </div>
      ),
    },
  ], [t, handleEditShop]);

  // Xử lý tạo cửa hàng mới
  const handleCreateShop = () => {
    setEditingShop(undefined);
    showForm();
  };

  // Xử lý submit form
  const handleSubmitShop = async (data: ShopInfoFormData) => {
    setIsSubmitting(true);
    try {
      // Kiểm tra xem có đang edit shop không
      const currentEditingShop = editingShop;

      if (currentEditingShop) {
        // Cập nhật cửa hàng hiện có
        await updateShopMutation.mutateAsync({
          id: currentEditingShop.id,
          data,
        });
      } else {
        // Tạo cửa hàng mới
        await createShopMutation.mutateAsync(data);
      }

      hideForm();
      setEditingShop(undefined);
    } catch (error) {
      console.error('Submit shop info error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý hủy form
  const handleCancelForm = () => {
    hideForm();
    setEditingShop(undefined);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Loading />
      </div>
    );
  }

  // Nếu có lỗi 404 (chưa có shop), hiển thị empty state
  const isNoShopList = error && (error as Error & { response?: { status: number } })?.response?.status === 404;

  // Debug logic
  console.log('🔍 [ShopPage] isNoShopList:', isNoShopList);
  console.log('🔍 [ShopPage] shopList exists:', !!shopList);
  console.log('🔍 [ShopPage] shopList length:', shopList?.length || 0);
  console.log('🔍 [ShopPage] table data:', shopList || []);

  return (
    <div>
      <MenuIconBar
        onAdd={handleCreateShop}
        items={[]}
        showDateFilter={false}
        showColumnFilter={false}
      />

      {/* SlideInForm cho tạo/cập nhật cửa hàng */}
      <SlideInForm isVisible={isFormVisible}>
        <ShopInfoForm
          shopInfo={editingShop} // Truyền thông tin shop khi edit
          onSubmit={handleSubmitShop}
          onCancel={handleCancelForm}
          isSubmitting={isSubmitting}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        {/* Hiển thị empty state nếu không có dữ liệu và không đang loading */}
        {(!shopList || shopList.length === 0) && !isLoading ? (
          <EmptyState
            icon="marketplace"
            title={t('business:shop.empty.title', 'Chưa có cửa hàng nào')}
            description={t('business:shop.empty.description', 'Tạo cửa hàng đầu tiên để bắt đầu quản lý đơn hàng và vận chuyển')}
            action={{
              label: t('business:shop.empty.action', 'Tạo cửa hàng'),
              onClick: handleCreateShop,
            }}
          />
        ) : (
          <Table
            columns={columns}
            data={shopList || []}
            rowKey="id"
            loading={isLoading}
            pagination={{
              current: 1,
              pageSize: 10,
              total: shopList?.length || 0,
              showSizeChanger: false,
              showFirstLastButtons: false,
              showPageInfo: true,
            }}
            emptyText={
              error && !isNoShopList
                ? t('business:shop.messages.loadError')
                : t('business:shop.empty.title')
            }
          />
        )}
      </Card>
    </div>
  );
};

export default ShopPage;
