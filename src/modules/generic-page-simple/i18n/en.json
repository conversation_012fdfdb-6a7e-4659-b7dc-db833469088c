{"genericPage": {"errors": {"title": "Error", "invalidPageId": "Invalid page ID", "pageNotFound": "Page not found", "fetchFailed": "Failed to fetch page configuration", "noConfig": {"title": "Configuration Error", "message": "No page configuration found"}, "componentNotFound": {"title": "Component Error", "message": "Component \"{{component}}\" not found"}}, "actions": {"submit": "Submit", "submitting": "Submitting...", "cancel": "Cancel", "back": "Back", "next": "Next", "save": "Save", "saving": "Saving..."}, "demo": {"title": "Generic Page Builder Demo", "description": "This is a demo of the Generic Page Builder. It shows how to create a dynamic page using JSON configuration.", "contactForm": {"title": "Contact Form", "description": "Please fill out the form below to contact us", "sections": {"personalInfo": {"title": "Personal Information", "description": "Please provide your personal information"}, "message": {"title": "Your Message"}, "preferences": {"title": "Preferences"}}, "fields": {"name": "Full Name", "email": "Email", "phone": "Phone Number", "subject": "Subject", "message": "Message", "priority": "Priority", "subscribe": "Subscribe to newsletter", "agree": "I agree to the terms and conditions"}, "placeholders": {"name": "Enter your full name", "email": "Enter your email address", "phone": "Enter your phone number", "subject": "Select a subject", "message": "Enter your message"}, "options": {"subject": {"general": "General Inquiry", "support": "Technical Support", "billing": "Billing Question", "feedback": "<PERSON><PERSON><PERSON>"}, "priority": {"low": "Low", "medium": "Medium", "high": "High"}}, "messages": {"terms": "By submitting this form, you agree to our terms of service and privacy policy.", "success": "Form submitted successfully!"}}}}}