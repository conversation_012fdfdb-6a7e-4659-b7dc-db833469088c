import { ReactNode } from 'react';

/**
 * Định nghĩa các kiểu component có thể sử dụng
 */
export type ComponentType =
  // Form components
  | 'text-input'
  | 'text-area'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'date-picker'

  // Display components
  | 'card'
  | 'typography'
  | 'image'
  | 'divider'
  | 'alert';

/**
 * Cấu hình cơ bản cho tất cả các component
 */
export interface BaseComponentConfig {
  /**
   * ID của component
   */
  id: string;

  /**
   * Loại component
   */
  type: ComponentType;

  /**
   * Nhãn hiển thị
   */
  label?: string;

  /**
   * Mô tả
   */
  description?: string;

  /**
   * Bắt buộc (cho form components)
   */
  required?: boolean;

  /**
   * Vô hiệu hóa
   */
  disabled?: boolean;

  /**
   * Chỉ đọc
   */
  readOnly?: boolean;

  /**
   * Giá trị mặc định
   */
  defaultValue?: unknown;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Văn bản trợ giúp
   */
  helperText?: string;

  /**
   * Class CSS tùy chỉnh
   */
  className?: string;
}

/**
 * Cấu hình cho text input
 */
export interface TextInputConfig extends BaseComponentConfig {
  type: 'text-input';
  inputType?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  minLength?: number;
  maxLength?: number;
}

/**
 * Cấu hình cho text area
 */
export interface TextAreaConfig extends BaseComponentConfig {
  type: 'text-area';
  rows?: number;
  minLength?: number;
  maxLength?: number;
}

/**
 * Cấu hình cho select
 */
export interface SelectConfig extends BaseComponentConfig {
  type: 'select';
  options: Array<{ label: string; value: string }>;
  multiple?: boolean;
}

/**
 * Cấu hình cho checkbox
 */
export interface CheckboxConfig extends BaseComponentConfig {
  type: 'checkbox';
}

/**
 * Cấu hình cho radio
 */
export interface RadioConfig extends BaseComponentConfig {
  type: 'radio';
  options: Array<{ label: string; value: string }>;
}

/**
 * Cấu hình cho date picker
 */
export interface DatePickerConfig extends BaseComponentConfig {
  type: 'date-picker';
  format?: string;
  minDate?: string;
  maxDate?: string;
}

/**
 * Cấu hình cho card
 */
export interface CardConfig extends BaseComponentConfig {
  type: 'card';
  title?: string;
  subtitle?: string;
  children: ComponentConfig[];
}

/**
 * Cấu hình cho typography
 */
export interface TypographyConfig extends BaseComponentConfig {
  type: 'typography';
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'subtitle1' | 'subtitle2';
  content: string;
}

/**
 * Cấu hình cho image
 */
export interface ImageConfig extends BaseComponentConfig {
  type: 'image';
  src: string;
  alt?: string;
  width?: number | string;
  height?: number | string;
}

/**
 * Cấu hình cho divider
 */
export interface DividerConfig extends BaseComponentConfig {
  type: 'divider';
}

/**
 * Cấu hình cho alert
 */
export interface AlertConfig extends BaseComponentConfig {
  type: 'alert';
  alertType?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  showIcon?: boolean;
  closable?: boolean;
}

/**
 * Union type cho tất cả các cấu hình component
 */
export type ComponentConfig =
  | TextInputConfig
  | TextAreaConfig
  | SelectConfig
  | CheckboxConfig
  | RadioConfig
  | DatePickerConfig
  | CardConfig
  | TypographyConfig
  | ImageConfig
  | DividerConfig
  | AlertConfig;

/**
 * Cấu hình cho một section
 */
export interface SectionConfig {
  /**
   * ID của section
   */
  id: string;

  /**
   * Tiêu đề của section
   */
  title?: string;

  /**
   * Mô tả của section
   */
  description?: string;

  /**
   * Các component trong section
   */
  components: ComponentConfig[];
}

/**
 * Cấu hình cho một trang
 */
export interface PageConfig {
  /**
   * ID của trang
   */
  id: string;

  /**
   * Tiêu đề của trang
   */
  title: string;

  /**
   * Mô tả của trang
   */
  description?: string;

  /**
   * Đường dẫn của trang
   */
  path: string;

  /**
   * Các section trong trang
   */
  sections: SectionConfig[];
}

/**
 * Props cho GenericPage component
 */
export interface GenericPageProps {
  /**
   * Cấu hình trang
   */
  config: PageConfig;

  /**
   * Giá trị ban đầu
   */
  initialValues?: Record<string, unknown>;

  /**
   * Xử lý khi submit form
   */
  onSubmit?: (values: Record<string, unknown>) => void;

  /**
   * Xử lý khi hủy
   */
  onCancel?: () => void;

  /**
   * Chỉ đọc
   */
  readOnly?: boolean;

  /**
   * Đang tải
   */
  isLoading?: boolean;

  /**
   * Các action bổ sung
   */
  actions?: ReactNode;
}

/**
 * Props cho ComponentRenderer
 */
export interface ComponentRendererProps {
  /**
   * Cấu hình component
   */
  config: ComponentConfig;

  /**
   * Giá trị
   */
  value?: unknown;

  /**
   * Xử lý khi thay đổi giá trị
   */
  onChange?: (value: unknown) => void;

  /**
   * Lỗi
   */
  error?: string;

  /**
   * Chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Định nghĩa registry cho các component
 */
export type ComponentRegistry = Record<ComponentType, React.ComponentType<ComponentRendererProps>>;
