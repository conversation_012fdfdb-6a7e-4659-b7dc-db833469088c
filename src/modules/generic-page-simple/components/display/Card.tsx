import React from 'react';
import { ComponentRendererProps, CardConfig } from '../../types/generic-page.types';
import { Card as CardComponent, Typography } from '@/shared/components/common';
import ComponentRenderer from '../ComponentRenderer';

/**
 * Component Card cho Generic Page Builder
 */
const Card: React.FC<ComponentRendererProps> = ({ config, value, onChange, error, readOnly }) => {
  // Cast config to CardConfig
  const cardConfig = config as CardConfig;

  return (
    <CardComponent className={cardConfig.className}>
      {(cardConfig.title || cardConfig.subtitle) && (
        <div className="p-4 border-b">
          {cardConfig.title && <Typography variant="h3">{cardConfig.title}</Typography>}
          {cardConfig.subtitle && (
            <Typography className="text-muted">{cardConfig.subtitle}</Typography>
          )}
        </div>
      )}
      <div className="p-4">
        {cardConfig.children.map(childConfig => (
          <div key={childConfig.id} className="mb-4">
            <ComponentRenderer
              config={childConfig}
              value={
                value && typeof value === 'object'
                  ? (value as Record<string, unknown>)[childConfig.id]
                  : undefined
              }
              onChange={childValue => {
                if (onChange && typeof value === 'object') {
                  onChange({
                    ...(value as Record<string, unknown>),
                    [childConfig.id]: childValue,
                  });
                }
              }}
              error={error}
              readOnly={readOnly}
            />
          </div>
        ))}
      </div>
    </CardComponent>
  );
};

export default Card;
