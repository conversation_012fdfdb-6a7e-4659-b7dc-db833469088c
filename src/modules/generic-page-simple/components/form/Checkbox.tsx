import React from 'react';
import { ComponentRendererProps, CheckboxConfig } from '../../types/generic-page.types';
import { Checkbox as CheckboxComponent, FormItem } from '@/shared/components/common';

/**
 * Component Checkbox cho Generic Page Builder
 */
const Checkbox: React.FC<ComponentRendererProps> = ({ config, value, onChange, readOnly }) => {
  // Cast config to CheckboxConfig
  const checkboxConfig = config as CheckboxConfig;

  return (
    <FormItem helpText={checkboxConfig.helperText} className={checkboxConfig.className}>
      <div className="flex items-center">
        <CheckboxComponent
          id={checkboxConfig.id}
          name={checkboxConfig.id}
          checked={Boolean(value)}
          onChange={checked => onChange?.(checked)}
          disabled={checkboxConfig.disabled || readOnly}
        />
        {checkboxConfig.label && (
          <label htmlFor={checkboxConfig.id} className="ml-2">
            {checkboxConfig.label}
            {checkboxConfig.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
      </div>
    </FormItem>
  );
};

export default Checkbox;
