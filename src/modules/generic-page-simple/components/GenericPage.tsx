import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { GenericPageProps, ComponentConfig } from '../types/generic-page.types';
import ComponentRenderer from './ComponentRenderer';
import { Card, Typography, Button, Loading } from '@/shared/components/common';
import Alert from './display/Alert';

/**
 * Component ch<PERSON>h để render trang từ cấu hình JSON
 */
const GenericPage: React.FC<GenericPageProps> = ({
  config,
  initialValues = {},
  onSubmit,
  onCancel,
  readOnly = false,
  isLoading = false,
  actions,
}) => {
  const { t } = useTranslation();
  const [values, setValues] = useState<Record<string, unknown>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Cập nhật giá trị khi initialValues thay đổi
  useEffect(() => {
    setValues(initialValues);
  }, [initialValues]);

  // Nếu đang tải, hiển thị loading
  if (isLoading) {
    return <Loading />;
  }

  // Nếu không có cấu hình, hiển thị thông báo lỗi
  if (!config) {
    return (
      <div className="p-4">
        <Alert
          config={{
            id: 'error-alert',
            type: 'alert',
            alertType: 'error',
            title: t('genericPage.errors.noConfig.title', 'Configuration Error'),
            message: t('genericPage.errors.noConfig.message', 'No page configuration found'),
          }}
        />
      </div>
    );
  }

  // Xử lý khi giá trị thay đổi
  const handleChange = (id: string, value: unknown) => {
    setValues(prev => ({
      ...prev,
      [id]: value,
    }));

    // Xóa lỗi nếu có
    if (errors[id]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // Xử lý khi submit form
  const handleSubmit = async () => {
    if (isSubmitting || !onSubmit) return;

    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render một component
  const renderComponent = (component: ComponentConfig) => {
    return (
      <div key={component.id} className="mb-4">
        <ComponentRenderer
          config={component}
          value={values[component.id]}
          onChange={value => handleChange(component.id, value)}
          error={errors[component.id]}
          readOnly={readOnly}
        />
      </div>
    );
  };

  // Render một section
  const renderSection = (section: (typeof config.sections)[0]) => {
    return (
      <Card key={section.id} className="mb-6">
        {(section.title || section.description) && (
          <div className="p-4 border-b">
            {section.title && <Typography variant="h3">{section.title}</Typography>}
            {section.description && (
              <Typography className="text-muted">{section.description}</Typography>
            )}
          </div>
        )}
        <div className="p-4">{section.components.map(component => renderComponent(component))}</div>
      </Card>
    );
  };

  return (
    <div className="generic-page">
      {/* Page Header */}
      <div className="mb-6">
        <Typography variant="h1">{config.title}</Typography>
        {config.description && <Typography className="text-muted">{config.description}</Typography>}
      </div>

      {/* Page Content */}
      <div className="space-y-6">{config.sections.map(renderSection)}</div>

      {/* Form Actions */}
      {(onSubmit || onCancel || actions) && (
        <div className="mt-6 flex justify-end space-x-4">
          {actions}
          {onCancel && (
            <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
              {t('genericPage.actions.cancel', 'Cancel')}
            </Button>
          )}
          {onSubmit && !readOnly && (
            <Button variant="primary" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting
                ? t('genericPage.actions.submitting', 'Submitting...')
                : t('genericPage.actions.submit', 'Submit')}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default GenericPage;
