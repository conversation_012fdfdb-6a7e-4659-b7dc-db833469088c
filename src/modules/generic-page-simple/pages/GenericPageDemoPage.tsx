import React from 'react';
import { useTranslation } from 'react-i18next';
import GenericPage from '../components/GenericPage';
import { PageConfig } from '../types/generic-page.types';
import { Card, Typography } from '@/shared/components/common';

/**
 * Trang demo cho Generic Page Builder
 */
const GenericPageDemoPage: React.FC = () => {
  const { t } = useTranslation();

  // Cấu hình trang mẫu
  const pageConfig: PageConfig = {
    id: 'contact-form',
    title: t('genericPage.demo.contactForm.title', 'Contact Form'),
    description: t(
      'genericPage.demo.contactForm.description',
      'Please fill out the form below to contact us'
    ),
    path: '/demo/contact-form',
    sections: [
      {
        id: 'personal-info',
        title: t(
          'genericPage.demo.contactForm.sections.personalInfo.title',
          'Personal Information'
        ),
        description: t(
          'genericPage.demo.contactForm.sections.personalInfo.description',
          'Please provide your personal information'
        ),
        components: [
          {
            id: 'name',
            type: 'text-input',
            label: t('genericPage.demo.contactForm.fields.name', 'Full Name'),
            required: true,
            placeholder: t(
              'genericPage.demo.contactForm.placeholders.name',
              'Enter your full name'
            ),
          },
          {
            id: 'email',
            type: 'text-input',
            label: t('genericPage.demo.contactForm.fields.email', 'Email'),
            required: true,
            inputType: 'email',
            placeholder: t(
              'genericPage.demo.contactForm.placeholders.email',
              'Enter your email address'
            ),
          },
          {
            id: 'phone',
            type: 'text-input',
            label: t('genericPage.demo.contactForm.fields.phone', 'Phone Number'),
            inputType: 'tel',
            placeholder: t(
              'genericPage.demo.contactForm.placeholders.phone',
              'Enter your phone number'
            ),
          },
        ],
      },
      {
        id: 'message',
        title: t('genericPage.demo.contactForm.sections.message.title', 'Your Message'),
        components: [
          {
            id: 'subject',
            type: 'select',
            label: t('genericPage.demo.contactForm.fields.subject', 'Subject'),
            required: true,
            placeholder: t('genericPage.demo.contactForm.placeholders.subject', 'Select a subject'),
            options: [
              {
                label: t('genericPage.demo.contactForm.options.subject.general', 'General Inquiry'),
                value: 'general',
              },
              {
                label: t(
                  'genericPage.demo.contactForm.options.subject.support',
                  'Technical Support'
                ),
                value: 'support',
              },
              {
                label: t(
                  'genericPage.demo.contactForm.options.subject.billing',
                  'Billing Question'
                ),
                value: 'billing',
              },
              {
                label: t('genericPage.demo.contactForm.options.subject.feedback', 'Feedback'),
                value: 'feedback',
              },
            ],
          },
          {
            id: 'message',
            type: 'text-area',
            label: t('genericPage.demo.contactForm.fields.message', 'Message'),
            required: true,
            placeholder: t(
              'genericPage.demo.contactForm.placeholders.message',
              'Enter your message'
            ),
            rows: 5,
          },
          {
            id: 'priority',
            type: 'radio',
            label: t('genericPage.demo.contactForm.fields.priority', 'Priority'),
            options: [
              {
                label: t('genericPage.demo.contactForm.options.priority.low', 'Low'),
                value: 'low',
              },
              {
                label: t('genericPage.demo.contactForm.options.priority.medium', 'Medium'),
                value: 'medium',
              },
              {
                label: t('genericPage.demo.contactForm.options.priority.high', 'High'),
                value: 'high',
              },
            ],
          },
        ],
      },
      {
        id: 'preferences',
        title: t('genericPage.demo.contactForm.sections.preferences.title', 'Preferences'),
        components: [
          {
            id: 'subscribe',
            type: 'checkbox',
            label: t('genericPage.demo.contactForm.fields.subscribe', 'Subscribe to newsletter'),
          },
          {
            id: 'terms',
            type: 'alert',
            alertType: 'info',
            message: t(
              'genericPage.demo.contactForm.messages.terms',
              'By submitting this form, you agree to our terms of service and privacy policy.'
            ),
          },
          {
            id: 'agree',
            type: 'checkbox',
            label: t(
              'genericPage.demo.contactForm.fields.agree',
              'I agree to the terms and conditions'
            ),
            required: true,
          },
        ],
      },
    ],
  };

  // Xử lý khi submit form
  const handleSubmit = async (values: Record<string, unknown>) => {
    console.log('Form submitted with values:', values);

    // Giả lập delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Hiển thị thông báo thành công
    alert(t('genericPage.demo.contactForm.messages.success', 'Form submitted successfully!'));
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <Card className="mb-6">
        <div className="p-4 border-b">
          <Typography variant="h2">
            {t('genericPage.demo.title', 'Generic Page Builder Demo')}
          </Typography>
        </div>
        <div className="p-4">
          <Typography>
            {t(
              'genericPage.demo.description',
              'This is a demo of the Generic Page Builder. It shows how to create a dynamic page using JSON configuration.'
            )}
          </Typography>
        </div>
      </Card>

      <GenericPage config={pageConfig} onSubmit={handleSubmit} />
    </div>
  );
};

export default GenericPageDemoPage;
