import React from 'react';
import { Select, FormItem } from '@/shared/components/common';
import { ComponentConfig } from '../../types/generic-page.types';

interface SelectDropdownProps extends ComponentConfig {
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
}

/**
 * Select dropdown component for generic page builder
 */
const SelectDropdown: React.FC<SelectDropdownProps> = ({
  id,
  label,
  placeholder,
  helperText,
  required,
  disabled,
  readOnly,
  value = '',
  onChange,
  error,
  size,
  className,
  options = [],
}) => {
  // Convert options to the format expected by Select component
  const selectOptions = Array.isArray(options)
    ? options.map(option => {
        if (typeof option === 'string') {
          return { label: option, value: option };
        }
        return option;
      })
    : [];

  return (
    <FormItem
      label={label}
      helpText={helperText}
      required={required}
      className={className as string}
    >
      <Select
        id={id}
        name={id}
        value={value}
        onChange={val => onChange?.(val as string)}
        placeholder={placeholder}
        disabled={disabled || readOnly}
        size={size === 'sm' || size === 'md' || size === 'lg' ? size : 'md'}
        error={error}
        options={selectOptions}
        fullWidth
      />
    </FormItem>
  );
};

export default SelectDropdown;
