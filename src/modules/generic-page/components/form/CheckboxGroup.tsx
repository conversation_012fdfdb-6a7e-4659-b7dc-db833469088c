import React from 'react';
import { Checkbox, FormItem } from '@/shared/components/common';
import { ComponentConfig } from '../../types/generic-page.types';

interface CheckboxGroupProps extends ComponentConfig {
  value?: string[];
  onChange?: (value: string[]) => void;
  error?: string;
}

/**
 * Checkbox group component for generic page builder
 */
const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  id,
  label,
  helperText,
  required,
  disabled,
  readOnly,
  value = [],
  onChange,
  className,
  options = [],
}) => {
  // Convert options to the format expected by Checkbox component
  const checkboxOptions = Array.isArray(options)
    ? options.map(option => {
        if (typeof option === 'string') {
          return { label: option, value: option };
        }
        return option;
      })
    : [];

  // Handle checkbox change
  const handleChange = (optionValue: string, checked: boolean) => {
    if (!onChange) return;

    if (checked) {
      onChange([...value, optionValue]);
    } else {
      onChange(value.filter(val => val !== optionValue));
    }
  };

  return (
    <FormItem
      label={label}
      helpText={helperText}
      required={required}
      className={className as string}
    >
      <div className="space-y-2">
        {checkboxOptions.map(option => (
          <div key={option.value} className="flex items-center">
            <Checkbox
              id={`${id}-${option.value}`}
              checked={value.includes(option.value)}
              onChange={checked => handleChange(option.value, checked)}
              disabled={disabled || readOnly}
            />
            <label htmlFor={`${id}-${option.value}`} className="ml-2 text-sm">
              {option.label}
            </label>
          </div>
        ))}
      </div>
    </FormItem>
  );
};

export default CheckboxGroup;
