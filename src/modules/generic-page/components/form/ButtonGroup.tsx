import React from 'react';
import { Button, FormItem } from '@/shared/components/common';
import { ComponentConfig } from '../../types/generic-page.types';

// Import các kiểu dữ liệu từ Button component
type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'outline'
  | 'success'
  | 'warning'
  | 'danger'
  | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonGroupProps extends Omit<ComponentConfig, 'error'> {
  value?: string;
  onChange?: (value: string) => void;
}

/**
 * Button group component for generic page builder
 */
const ButtonGroup: React.FC<ButtonGroupProps> = ({
  label,
  helperText,
  required,
  disabled,
  readOnly,
  value = '',
  onChange,
  className,
  options = [],
  variant = 'outline',
  size = 'md',
}) => {
  // Convert options to the format expected by Button component
  const buttonOptions = Array.isArray(options)
    ? options.map(option => {
        if (typeof option === 'string') {
          return { label: option, value: option };
        }
        return option;
      })
    : [];

  return (
    <FormItem
      label={label as React.ReactNode}
      helpText={helperText as React.ReactNode}
      required={required as boolean | undefined}
      className={className as string}
    >
      <div className="flex flex-wrap gap-2">
        {buttonOptions.map(option => (
          <Button
            key={option.value}
            variant={value === option.value ? 'primary' : (variant as ButtonVariant)}
            size={size as ButtonSize}
            onClick={() => onChange?.(option.value)}
            disabled={(disabled || readOnly) as boolean | undefined}
          >
            {option.label}
          </Button>
        ))}
      </div>
    </FormItem>
  );
};

export default ButtonGroup;
