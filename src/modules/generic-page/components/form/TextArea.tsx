import React from 'react';
import { Textarea, FormItem } from '@/shared/components/common';
import { ComponentConfig } from '../../types/generic-page.types';

interface TextAreaProps extends ComponentConfig {
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  rows?: number;
}

/**
 * Text area component for generic page builder
 */
const TextArea: React.FC<TextAreaProps> = React.memo(
  ({
    id,
    label,
    placeholder,
    helperText,
    required,
    disabled,
    readOnly,
    value = '',
    onChange,
    error,
    size,
    className,
    rows = 4,
  }) => {
    // Handle textarea change
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange?.(e.target.value);
    };

    return (
      <FormItem
        label={label}
        helpText={helperText}
        required={required}
        className={typeof className === 'string' ? className : undefined}
      >
        <Textarea
          id={id}
          name={id}
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly}
          size={size === 'sm' || size === 'md' || size === 'lg' ? size : 'md'}
          status={error ? 'error' : 'default'}
          rows={rows}
          fullWidth
        />
      </FormItem>
    );
  }
);

// Display name for debugging
TextArea.displayName = 'TextArea';

export default TextArea;
