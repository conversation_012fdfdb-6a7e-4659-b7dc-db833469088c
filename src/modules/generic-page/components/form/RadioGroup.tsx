import React from 'react';
import { Radio, FormItem } from '@/shared/components/common';
import { ComponentConfig } from '../../types/generic-page.types';

interface RadioGroupProps extends Omit<ComponentConfig, 'error'> {
  value?: string;
  onChange?: (value: string) => void;
}

/**
 * Radio group component for generic page builder
 */
const RadioGroup: React.FC<RadioGroupProps> = ({
  id,
  label,
  helperText,
  required,
  disabled,
  readOnly,
  value = '',
  onChange,
  className,
  options = [],
}) => {
  // Convert options to the format expected by Radio component
  const radioOptions = Array.isArray(options)
    ? options.map(option => {
        if (typeof option === 'string') {
          return { label: option, value: option };
        }
        return option;
      })
    : [];

  return (
    <FormItem
      label={label as React.ReactNode}
      helpText={helperText as React.ReactNode}
      required={required as boolean | undefined}
      className={className as string}
    >
      <div className="space-y-2">
        {radioOptions.map(option => (
          <div key={option.value} className="flex items-center">
            <Radio
              id={`${id}-${option.value}`}
              name={id as string}
              value={option.value}
              checked={value === option.value}
              onChange={() => onChange?.(option.value)}
              disabled={(disabled || readOnly) as boolean | undefined}
            />
            <label htmlFor={`${id}-${option.value}`} className="ml-2 text-sm">
              {option.label}
            </label>
          </div>
        ))}
      </div>
    </FormItem>
  );
};

export default RadioGroup;
