import React from 'react';
import { ComponentConfig } from '../../types/generic-page.types';

interface SpacerProps extends ComponentConfig {
  height?: number | string;
  width?: number | string;
  className?: string;
}

/**
 * Spacer component for adding empty space
 */
const Spacer: React.FC<SpacerProps> = ({ height = '1rem', width = '100%', className = '' }) => {
  return (
    <div
      className={className}
      style={{
        height: typeof height === 'number' ? `${height}px` : height,
        width: typeof width === 'number' ? `${width}px` : width,
      }}
    />
  );
};

export default Spacer;
