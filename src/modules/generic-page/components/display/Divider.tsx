import React from 'react';
import { Typography } from '@/shared/components/common';
import { ComponentConfig } from '../../types/generic-page.types';

interface DividerProps extends ComponentConfig {
  orientation?: 'horizontal' | 'vertical';
  type?: 'solid' | 'dashed' | 'dotted';
  color?: string;
  thickness?: number;
  withLabel?: boolean;
}

/**
 * Divider component for separating content
 */
const Divider: React.FC<DividerProps> = ({
  label,
  orientation = 'horizontal',
  type = 'solid',
  color = 'gray',
  thickness = 1,
  withLabel = false,
  className,
}) => {
  // Determine border style
  const borderStyle =
    type === 'solid' ? 'border-solid' : type === 'dashed' ? 'border-dashed' : 'border-dotted';

  // Determine border color
  const borderColor = `border-${color}-300 dark:border-${color}-700`;

  // Determine border thickness
  const borderThickness = `border-${thickness}`;

  if (orientation === 'vertical') {
    return (
      <div
        className={`h-full mx-2 border-l ${borderStyle} ${borderColor} ${borderThickness} ${className || ''}`}
      />
    );
  }

  if (withLabel && label) {
    return (
      <div className={`flex items-center my-4 ${className || ''}`}>
        <div className={`flex-grow border-t ${borderStyle} ${borderColor} ${borderThickness}`} />
        <Typography className="mx-4 text-muted">{label}</Typography>
        <div className={`flex-grow border-t ${borderStyle} ${borderColor} ${borderThickness}`} />
      </div>
    );
  }

  return (
    <div
      className={`w-full my-4 border-t ${borderStyle} ${borderColor} ${borderThickness} ${className || ''}`}
    />
  );
};

export default Divider;
