/**
 * Trang xác nhận thanh toán thành công
 */
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { Typography, Container, Button, Card, Icon, Image } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { ServiceType, SubscriptionDuration } from '../types';
import { PaymentMethod } from '../components/PaymentMethodSelector';

/**
 * Thông tin đơn hàng
 */
interface OrderInfo {
  packageName: string;
  packageType: ServiceType;
  duration: SubscriptionDuration;
  price: number;
  discount: number;
  total: number;
  paymentMethod: PaymentMethod;
  orderNumber: string;
  paymentDate: string;
}

/**
 * Trang xác nhận thanh toán thành công
 */
const PaymentSuccessPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;

  // Sử dụng hook theme
  useTheme();

  // Lấy thông tin đơn hàng từ state
  const orderInfo: OrderInfo = state?.orderInfo || {
    packageName: '',
    packageType: ServiceType.MAIN,
    duration: SubscriptionDuration.MONTHLY,
    price: 0,
    discount: 0,
    total: 0,
    paymentMethod: PaymentMethod.R_POINT,
    orderNumber: '',
    paymentDate: new Date().toISOString(),
  };

  // Chuyển hướng về trang gói dịch vụ nếu không có thông tin đơn hàng
  useEffect(() => {
    if (!state?.orderInfo) {
      navigate('/subscription/packages');
    }
  }, [state, navigate]);

  // Xử lý khi quay lại trang chủ
  const handleBackToHome = () => {
    navigate('/');
  };

  // Xử lý khi xem chi tiết đơn hàng
  const handleViewDetails = () => {
    navigate('/subscription/orders');
  };

  // Hiển thị tên phương thức thanh toán
  const getPaymentMethodName = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.BANK_TRANSFER:
        return t('subscription.payment.bankTransfer', 'Chuyển khoản ngân hàng');
      case PaymentMethod.CREDIT_CARD:
        return t('subscription.payment.creditCard', 'Thẻ tín dụng');
      case PaymentMethod.E_WALLET:
        return t('subscription.payment.eWallet', 'Ví điện tử');
      case PaymentMethod.R_POINT:
        return t('subscription.payment.rPoint', 'R-Point');
      default:
        return '';
    }
  };

  // Hiển thị tên thời hạn
  const getDurationName = (duration: SubscriptionDuration) => {
    switch (duration) {
      case SubscriptionDuration.MONTHLY:
        return t('subscription.duration.monthly', 'Hàng tháng');
      case SubscriptionDuration.SEMI_ANNUAL:
        return t('subscription.duration.semiAnnual', '6 tháng');
      case SubscriptionDuration.ANNUAL:
        return t('subscription.duration.annual', 'Hàng năm');
      default:
        return '';
    }
  };

  return (
    <Container className="py-8">
      <div className="max-w-2xl mx-auto">
        {/* Thông báo thành công */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 mb-4">
            <Icon name="check" size="lg" className="text-green-600 dark:text-green-300" />
          </div>
          <Typography variant="h3" className="font-bold mb-2">
            {t('subscription.payment.success', 'Thanh toán thành công!')}
          </Typography>
          <Typography variant="body1" className="text-muted">
            {t(
              'subscription.payment.successMessage',
              'Cảm ơn bạn đã đăng ký dịch vụ. Gói dịch vụ của bạn đã được kích hoạt.'
            )}
          </Typography>
        </div>

        {/* Chi tiết đơn hàng */}
        <Card className="p-6 mb-6">
          <Typography variant="h5" className="font-bold mb-4">
            {t('subscription.payment.orderDetails', 'Chi tiết đơn hàng')}
          </Typography>

          <div className="space-y-4">
            {/* Thông tin gói */}
            <div className="flex justify-between pb-4 border-b border-border">
              <Typography variant="body2" className="text-muted">
                {t('subscription.payment.packageName', 'Tên gói')}
              </Typography>
              <Typography variant="body1" className="font-semibold">
                {orderInfo.packageName}
              </Typography>
            </div>

            {/* Thời hạn */}
            <div className="flex justify-between pb-4 border-b border-border">
              <Typography variant="body2" className="text-muted">
                {t('subscription.payment.duration', 'Thời hạn')}
              </Typography>
              <Typography variant="body1" className="font-semibold">
                {getDurationName(orderInfo.duration)}
              </Typography>
            </div>

            {/* Phương thức thanh toán */}
            <div className="flex justify-between pb-4 border-b border-border">
              <Typography variant="body2" className="text-muted">
                {t('subscription.payment.paymentMethod', 'Phương thức thanh toán')}
              </Typography>
              <div className="flex items-center">
                {orderInfo.paymentMethod === PaymentMethod.R_POINT && (
                  <Image
                    src="/src/shared/assets/images/rpoint.png"
                    alt="R-Point"
                    width={20}
                    height={20}
                    className="mr-2"
                  />
                )}
                <Typography variant="body1" className="font-semibold">
                  {getPaymentMethodName(orderInfo.paymentMethod)}
                </Typography>
              </div>
            </div>

            {/* Mã đơn hàng */}
            <div className="flex justify-between pb-4 border-b border-border">
              <Typography variant="body2" className="text-muted">
                {t('subscription.payment.orderNumber', 'Mã đơn hàng')}
              </Typography>
              <Typography variant="body1" className="font-semibold">
                {orderInfo.orderNumber}
              </Typography>
            </div>

            {/* Ngày thanh toán */}
            <div className="flex justify-between pb-4 border-b border-border">
              <Typography variant="body2" className="text-muted">
                {t('subscription.payment.paymentDate', 'Ngày thanh toán')}
              </Typography>
              <Typography variant="body1" className="font-semibold">
                {new Date(orderInfo.paymentDate).toLocaleDateString('vi-VN')}
              </Typography>
            </div>

            {/* Tổng tiền */}
            <div className="flex justify-between pt-2">
              <Typography variant="body1" className="font-semibold">
                {t('subscription.payment.total', 'Tổng tiền')}
              </Typography>
              <div className="flex items-center">
                <Typography variant="h5" className="font-bold text-primary mr-1">
                  {Math.round(orderInfo.total / 1000)}
                </Typography>
                <Image
                  src="/src/shared/assets/images/rpoint.png"
                  alt="R-Point"
                  width={20}
                  height={20}
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Nút điều hướng */}
        <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
          <Button variant="outline" onClick={handleBackToHome}>
            {t('subscription.payment.backToHome', 'Quay lại trang chủ')}
          </Button>
          <Button variant="primary" onClick={handleViewDetails}>
            {t('subscription.payment.viewDetails', 'Xem chi tiết đơn hàng')}
          </Button>
        </div>
      </div>
    </Container>
  );
};

export default PaymentSuccessPage;
