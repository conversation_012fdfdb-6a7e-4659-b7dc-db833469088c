/**
 * Hook quản lý dữ liệu gói dịch vụ
 */
import { useState, useMemo } from 'react';
import { ServicePackage, ServiceType, SubscriptionDuration } from '../types';

// D<PERSON> liệu mẫu cho các gói dịch vụ
const defaultPackages: ServicePackage[] = [
  // G<PERSON><PERSON> dịch vụ chính
  {
    id: 'main-basic',
    name: '<PERSON><PERSON> bản',
    type: ServiceType.MAIN,
    description: '<PERSON><PERSON><PERSON> dịch vụ cơ bản cho doanh nghiệp nhỏ',
    prices: {
      [SubscriptionDuration.MONTHLY]: 99000,
      [SubscriptionDuration.SEMI_ANNUAL]: 534600, // Giảm 10%
      [SubscriptionDuration.ANNUAL]: 950400, // Giảm 20%
    },
    features: [
      { name: 'Số lượng người dùng', value: 5 },
      { name: 'Dung lượng lưu trữ', value: '5GB' },
      { name: 'Hỗ tr<PERSON> kỹ thuật', value: 'Email' },
      { name: '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> cơ bản', value: true },
      { name: '<PERSON><PERSON><PERSON> hợp API', value: false },
    ],
  },
  {
    id: 'main-pro',
    name: '<PERSON><PERSON><PERSON><PERSON> nghiệp',
    type: ServiceType.MAIN,
    description: 'Gói dịch vụ chuyên nghiệp cho doanh nghiệp vừa',
    prices: {
      [SubscriptionDuration.MONTHLY]: 199000,
      [SubscriptionDuration.SEMI_ANNUAL]: 1074600, // Giảm 10%
      [SubscriptionDuration.ANNUAL]: 1910400, // Giảm 20%
    },
    features: [
      { name: 'Số lượng người dùng', value: 20 },
      { name: 'Dung lượng lưu trữ', value: '20GB' },
      { name: 'Hỗ trợ kỹ thuật', value: 'Email, Chat' },
      { name: 'Báo cáo nâng cao', value: true },
      { name: 'Tích hợp API', value: true },
      { name: 'Quản lý quyền', value: true },
    ],
    isPopular: true,
  },
  {
    id: 'main-enterprise',
    name: 'Doanh nghiệp',
    type: ServiceType.MAIN,
    description: 'Gói dịch vụ cao cấp cho doanh nghiệp lớn',
    prices: {
      [SubscriptionDuration.MONTHLY]: 399000,
      [SubscriptionDuration.SEMI_ANNUAL]: 2154600, // Giảm 10%
      [SubscriptionDuration.ANNUAL]: 3830400, // Giảm 20%
    },
    features: [
      { name: 'Số lượng người dùng', value: 'Không giới hạn' },
      { name: 'Dung lượng lưu trữ', value: '100GB' },
      { name: 'Hỗ trợ kỹ thuật', value: '24/7 ưu tiên' },
      { name: 'Báo cáo tùy chỉnh', value: true },
      { name: 'Tích hợp API', value: true },
      { name: 'Quản lý quyền nâng cao', value: true },
      { name: 'Tùy chỉnh theo yêu cầu', value: true },
    ],
  },

  // Gói tính năng
  {
    id: 'feature-chat',
    name: 'Live Chat',
    type: ServiceType.FEATURE,
    description: 'Gói tính năng live chat cho doanh nghiệp',
    prices: {
      [SubscriptionDuration.MONTHLY]: 149000,
      [SubscriptionDuration.SEMI_ANNUAL]: 804600, // Giảm 10%
      [SubscriptionDuration.ANNUAL]: 1430400, // Giảm 20%
    },
    features: [
      { name: 'Số lượng cuộc hội thoại', value: '2000/tháng' },
      { name: 'Số lượng nhân viên', value: 10 },
      { name: 'Chatbot nâng cao', value: true },
      { name: 'Tích hợp website', value: true },
      { name: 'Tích hợp mạng xã hội', value: true },
      { name: 'Phân tích hội thoại', value: true },
    ],
    isPopular: true,
  },
  {
    id: 'feature-payment',
    name: 'Thanh toán',
    type: ServiceType.FEATURE,
    description: 'Gói tính năng thanh toán cho doanh nghiệp',
    prices: {
      [SubscriptionDuration.MONTHLY]: 199000,
      [SubscriptionDuration.SEMI_ANNUAL]: 1074600, // Giảm 10%
      [SubscriptionDuration.ANNUAL]: 1910400, // Giảm 20%
    },
    features: [
      { name: 'Số lượng giao dịch', value: '2000/tháng' },
      { name: 'Phí giao dịch', value: '2.5% + 2,000đ' },
      { name: 'Phương thức thanh toán', value: 'Thẻ nội địa, QR Code, Thẻ quốc tế' },
      { name: 'Báo cáo nâng cao', value: true },
      { name: 'Tích hợp website', value: true },
      { name: 'Thanh toán định kỳ', value: true },
    ],
  },
  {
    id: 'feature-marketing',
    name: 'Marketing',
    type: ServiceType.FEATURE,
    description: 'Gói tính năng marketing cho doanh nghiệp',
    prices: {
      [SubscriptionDuration.MONTHLY]: 249000,
      [SubscriptionDuration.SEMI_ANNUAL]: 1344600, // Giảm 10%
      [SubscriptionDuration.ANNUAL]: 2390400, // Giảm 20%
    },
    features: [
      { name: 'Số lượng email', value: '20,000/tháng' },
      { name: 'Số lượng chiến dịch', value: 20 },
      { name: 'Mẫu email', value: 'Nâng cao' },
      { name: 'Phân tích chi tiết', value: true },
      { name: 'Tự động hóa', value: true },
      { name: 'Phân đoạn khách hàng', value: true },
    ],
  },
];

/**
 * Hook cung cấp dữ liệu và logic cho các gói dịch vụ
 */
export const useSubscriptionPackages = (serviceType?: ServiceType) => {
  // State lưu trữ thời hạn đăng ký đang chọn
  const [selectedDuration, setSelectedDuration] = useState<SubscriptionDuration>(
    SubscriptionDuration.MONTHLY
  );

  // Lọc gói dịch vụ theo loại
  const packages = useMemo(() => {
    if (!serviceType) return defaultPackages;
    return defaultPackages.filter(pkg => pkg.type === serviceType);
  }, [serviceType]);

  return {
    packages,
    selectedDuration,
    setSelectedDuration,
  };
};
