/**
 * Component cho phép chọn phương thức thanh toán
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Radio, Icon, Image } from '@/shared/components/common';

/**
 * Phương thức thanh toán
 */
enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  CREDIT_CARD = 'credit_card',
  E_WALLET = 'e_wallet',
  R_POINT = 'r_point',
}

export { PaymentMethod };

interface PaymentMethodSelectorProps {
  /**
   * Phương thức thanh toán đang chọn
   */
  selectedMethod: PaymentMethod;

  /**
   * Callback khi thay đổi phương thức thanh toán
   */
  onMethodChange: (method: PaymentMethod) => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component cho phép chọn phương thức thanh toán
 */
const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethod,
  onMethodChange,
  className = '',
}) => {
  const { t } = useTranslation();

  // Danh sách phương thức thanh toán
  const paymentMethods = [
    {
      value: PaymentMethod.R_POINT,
      label: t('subscription.order.rPoint', 'Thanh toán bằng R-Point'),
      icon: 'coin' as const,
      customIcon: true,
      iconPath: '/src/shared/assets/images/rpoint.png',
    },
    {
      value: PaymentMethod.BANK_TRANSFER,
      label: t('subscription.order.bankTransfer'),
      icon: 'credit-card' as const,
    },
    {
      value: PaymentMethod.CREDIT_CARD,
      label: t('subscription.order.creditCard'),
      icon: 'credit-card' as const,
    },
    {
      value: PaymentMethod.E_WALLET,
      label: t('subscription.order.eWallet'),
      icon: 'payment' as const,
    },
  ];

  return (
    <Card className={`p-6 ${className}`}>
      <Typography variant="h5" className="font-bold mb-4">
        {t('subscription.order.paymentMethod')}
      </Typography>

      <div className="space-y-3">
        {paymentMethods.map(method => (
          <div
            key={method.value}
            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
              selectedMethod === method.value
                ? 'border-primary bg-primary/5'
                : 'border-border hover:border-primary/30'
            }`}
            onClick={() => onMethodChange(method.value)}
          >
            <div className="flex items-center">
              <Radio
                checked={selectedMethod === method.value}
                onChange={() => onMethodChange(method.value)}
              />
              <div className="ml-3 flex items-center">
                {method.customIcon ? (
                  <Image
                    src={method.iconPath}
                    alt="R-Point"
                    width={24}
                    height={24}
                    className="mr-2"
                  />
                ) : (
                  <Icon name={method.icon} size="md" className="mr-2" />
                )}
                <Typography variant="body1">{method.label}</Typography>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default PaymentMethodSelector;
