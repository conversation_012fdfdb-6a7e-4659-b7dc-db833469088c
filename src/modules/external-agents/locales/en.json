{"title": "External Agents", "subtitle": "Manage external agents", "description": "Connect and manage external agents through various protocols", "navigation": {"overview": "Overview", "agents": "Agents List", "protocols": "Protocols", "templates": "Protocol Templates", "webhooks": "Webhooks", "analytics": "Analytics", "messages": "Messages", "settings": "Settings"}, "agent": {"title": "Agent", "name": "Agent name", "description": "Description", "status": "Status", "protocol": "Protocol", "endpoint": "Endpoint", "capabilities": "Capabilities", "tags": "Tags", "created": "Created", "updated": "Updated", "lastConnected": "Last connected", "version": "Version", "isActive": "Active"}, "status": {"active": "Active", "inactive": "Inactive", "connecting": "Connecting", "error": "Error", "maintenance": "Maintenance"}, "protocol": {"mcp": "Model Context Protocol", "google_agent": "Google Agent Communication", "rest_api": "REST API", "websocket": "WebSocket", "grpc": "gRPC", "custom": "Custom Protocol"}, "authentication": {"none": "No Authentication", "api_key": "API Key", "bearer_token": "<PERSON><PERSON>", "oauth2": "OAuth 2.0", "basic_auth": "Basic Authentication", "custom": "Custom Authentication"}, "capabilities": {"text_processing": "Text Processing", "image_analysis": "Image Analysis", "data_retrieval": "Data Retrieval", "file_operations": "File Operations", "api_calls": "API Calls", "real_time_communication": "Real-time Communication", "custom": "Custom"}, "stats": {"totalAgents": "Total Agents", "activeAgents": "Active Agents", "inactiveAgents": "Inactive Agents", "errorAgents": "Error Agents"}, "actions": {"create": "Create", "edit": "Edit", "delete": "Delete", "test": "Test", "connect": "Connect", "disconnect": "Disconnect", "refresh": "Refresh", "export": "Export", "import": "Import", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "retry": "Retry"}, "form": {"required": "Required", "optional": "Optional", "placeholder": {"name": "Enter agent name", "description": "Enter agent description", "endpoint": "https://api.example.com", "apiKey": "Enter API key", "token": "Enter token", "username": "Enter username", "password": "Enter password", "search": "Search agents..."}, "validation": {"required": "This field is required", "invalidUrl": "Invalid URL", "invalidEmail": "Invalid email", "minLength": "Minimum {min} characters", "maxLength": "Maximum {max} characters"}}, "messages": {"success": {"created": "Agent created successfully", "updated": "Agent updated successfully", "deleted": "Agent deleted successfully", "connected": "Connected successfully", "tested": "Connection test successful"}, "error": {"createFailed": "Failed to create agent", "updateFailed": "Failed to update agent", "deleteFailed": "Failed to delete agent", "connectionFailed": "Connection failed", "testFailed": "Connection test failed", "loadFailed": "Failed to load data"}}, "connection": {"test": "Test Connection", "testing": "Testing...", "success": "Connection successful", "failed": "Connection failed", "timeout": "Connection timeout", "responseTime": "Response time", "lastTest": "Last test"}, "performance": {"title": "Performance", "totalRequests": "Total requests", "successfulRequests": "Successful requests", "failedRequests": "Failed requests", "averageResponseTime": "Average response time", "uptime": "Uptime", "successRate": "Success rate"}, "filters": {"all": "All", "status": "Status", "protocol": "Protocol", "capabilities": "Capabilities", "tags": "Tags", "dateRange": "Date range", "clear": "Clear filters"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "items": "items", "showing": "Showing", "to": "to"}, "empty": {"noAgents": "No agents yet", "noMessages": "No messages yet", "noResults": "No results found", "createFirst": "Create your first agent"}, "loading": {"agents": "Loading agents...", "messages": "Loading messages...", "testing": "Testing connection...", "saving": "Saving...", "deleting": "Deleting..."}}