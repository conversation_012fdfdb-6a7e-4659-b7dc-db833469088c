import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Chip, ActionMenu } from '@/shared/components/common';
import StatusIndicator from '../indicators/StatusIndicator';
import ProtocolBadge from '../indicators/ProtocolBadge';
import { ExternalAgent } from '../../types';
import { formatRelativeTime, formatUrl, formatCapabilities } from '../../utils';

interface ExternalAgentCardProps {
  agent: ExternalAgent;
  onEdit?: (agent: ExternalAgent) => void;
  onDelete?: (agent: ExternalAgent) => void;
  onTest?: (agent: ExternalAgent) => void;
  onView?: (agent: ExternalAgent) => void;
  className?: string;
}

const ExternalAgentCard: React.FC<ExternalAgentCardProps> = ({
  agent,
  onEdit,
  onDelete,
  onTest,
  onView,
  className,
}) => {
  const { t } = useTranslation(['common', 'external-agents']);

  const handleCardClick = () => {
    onView?.(agent);
  };

  const menuItems = [
    {
      id: 'edit',
      label: t('external-agents:actions.edit'),
      onClick: () => onEdit?.(agent),
      icon: 'edit',
    },
    {
      id: 'test',
      label: t('external-agents:actions.test'),
      onClick: () => onTest?.(agent),
      icon: 'test-tube',
    },
    {
      id: 'delete',
      label: t('external-agents:actions.delete'),
      onClick: () => onDelete?.(agent),
      icon: 'trash',
      variant: 'destructive' as const,
    },
  ];

  return (
    <Card 
      className={`w-full bg-background text-foreground hover:shadow-md transition-shadow cursor-pointer ${className}`}
      onClick={handleCardClick}
    >
      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <Typography variant="h3" className="font-semibold truncate">
              {agent.name}
            </Typography>
            <div className="flex items-center gap-2 mt-1">
              <StatusIndicator status={agent.status} />
              <ProtocolBadge protocol={agent.protocol} />
            </div>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <ActionMenu items={menuItems} />
          </div>
        </div>

        {/* Description */}
        {agent.description && (
          <Typography variant="body2" className="text-muted-foreground mb-3 line-clamp-2">
            {agent.description}
          </Typography>
        )}

        {/* Endpoint */}
        <div className="mb-3">
          <Typography variant="caption" className="text-muted-foreground">
            {t('external-agents:agent.endpoint')}
          </Typography>
          <Typography variant="body2" className="font-mono text-sm truncate">
            {formatUrl(agent.endpoint)}
          </Typography>
        </div>

        {/* Capabilities */}
        {agent.capabilities && agent.capabilities.length > 0 && (
          <div className="mb-3">
            <Typography variant="caption" className="text-muted-foreground">
              {t('external-agents:agent.capabilities')}
            </Typography>
            <Typography variant="body2" className="text-sm">
              {formatCapabilities(agent.capabilities)}
            </Typography>
          </div>
        )}

        {/* Tags */}
        {agent.tags && agent.tags.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {agent.tags.slice(0, 3).map((tag, index) => (
                <Chip key={index} size="sm">
                  {tag}
                </Chip>
              ))}
              {agent.tags.length > 3 && (
                <Chip size="sm">
                  +{agent.tags.length - 3}
                </Chip>
              )}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
          <div>
            <Typography variant="caption">
              {t('external-agents:agent.updated')}: {formatRelativeTime(agent.updatedAt)}
            </Typography>
          </div>
          {agent.lastConnectedAt && (
            <div>
              <Typography variant="caption">
                {t('external-agents:agent.lastConnected')}: {formatRelativeTime(agent.lastConnectedAt)}
              </Typography>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default ExternalAgentCard;
