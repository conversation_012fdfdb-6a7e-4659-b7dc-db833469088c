import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Select, Slider, Checkbox, Textarea } from '@/shared/components/common';
import AsyncSelect from '@/shared/components/common/Select/AsyncSelect';
import {
  useGetProvidersForAgentConfig,
  useGetModelsByProvider,
  normalizeModelConfig
} from '../../hooks/useBaseModel';

import { TypeProviderEnum } from '../../types';



interface ModelConfigData {
  provider: TypeProviderEnum | 'redai'; // Thêm 'redai' cho provider mặc định
  providerId?: string; // ID của user provider (nếu không phải RedAI)
  modelId: string;
  vectorStore: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
  instruction?: string;
}

interface ModelConfigProps {
  initialData?: ModelConfigData;
  onSave?: (data: ModelConfigData) => void;
}

/**
 * Component cấu hình model AI cho Agent
 */
const ModelConfig: React.FC<ModelConfigProps> = ({
  initialData,
  onSave
}) => {
  const [configData, setConfigData] = useState<ModelConfigData>(initialData || {
    provider: 'redai', // Mặc định sử dụng RedAI
    modelId: 'gpt-4',
    vectorStore: 'pinecone',
    maxTokens: 1229,
    temperature: 1,
    topP: 1,
    topK: 100,
    instruction: ''
  });

  // State để kiểm soát hiển thị cấu hình nâng cao
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);

  // Lấy danh sách providers (bao gồm RedAI mặc định và user providers)
  const {
    providers
  } = useGetProvidersForAgentConfig();

  // Lấy danh sách models dựa trên provider được chọn
  const {
    models: modelOptions,
    isLoading: isLoadingModels,
    error: modelsError
  } = useGetModelsByProvider(
    configData.provider === 'redai' ? 'redai' : configData.providerId || null,
    configData.provider === 'redai'
  );

  // Fallback options nếu API chưa load hoặc lỗi
  const fallbackOptions = useMemo(() => [
    {
      value: 'loading',
      label: isLoadingModels ? 'Đang tải...' : modelsError ? 'Lỗi tải model' : 'Không có model'
    }
  ], [isLoadingModels, modelsError]);

  // Lấy config của model hiện tại được chọn
  const selectedModelConfig = useMemo(() => {
    if (!configData.modelId || !modelOptions.length) {
      return null;
    }

    const selectedModel = modelOptions.find(option => option.value === configData.modelId);
    if (!selectedModel?.data) {
      return null;
    }

    const rawConfig = selectedModel.data.config || null;
    const normalizedConfig = normalizeModelConfig(rawConfig);

    return normalizedConfig;
  }, [configData.modelId, modelOptions]);

  // Auto-update model khi có data từ API và model hiện tại không có trong danh sách
  useEffect(() => {
    if (modelOptions.length > 0) {
      const currentModelExists = modelOptions.some(option => option.value === configData.modelId);
      if (!currentModelExists) {
        // Chọn model đầu tiên trong danh sách
        const firstModel = modelOptions[0];
        const newConfigData = {
          ...configData,
          modelId: firstModel.value
        };

        setConfigData(newConfigData);

        if (onSave) {
          onSave(newConfigData);
        }
      }
    }
  }, [modelOptions, configData, onSave]);

  // Xử lý khi thay đổi select
  const handleSelectChange = (name: string, value: string | number | string[] | number[]) => {
    setConfigData(prev => ({
      ...prev,
      [name]: value
    }));

    if (onSave) {
      onSave({
        ...configData,
        [name]: value
      });
    }
  };

  // Xử lý khi thay đổi slider
  const handleSliderChange = (name: string, value: number) => {
    setConfigData(prev => ({
      ...prev,
      [name]: value
    }));

    if (onSave) {
      onSave({
        ...configData,
        [name]: value
      });
    }
  };

  // Xử lý khi thay đổi textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setConfigData(prev => ({
      ...prev,
      [name]: value
    }));

    if (onSave) {
      onSave({
        ...configData,
        [name]: value
      });
    }
  };

  // Xử lý khi thay đổi checkbox
  const handleCheckboxChange = (checked: boolean) => {
    setShowAdvancedConfig(checked);
  };

  // Provider options cho AsyncSelect - bao gồm RedAI và user providers
  const allProviderOptions = useMemo(() => {
    return providers.map(provider => ({
      value: provider.id,
      label: provider.isDefault ? `${provider.name} (Mặc định)` : provider.name,
      icon: provider.icon,
      providerId: provider.isDefault ? undefined : provider.id,
      providerType: provider.type,
      isDefault: provider.isDefault,
    }));
  }, [providers]);

  // Load options function cho AsyncSelect
  const loadProviderOptions = useCallback(async (inputValue: string) => {
    // Filter providers based on search input
    const filteredOptions = allProviderOptions.filter(option =>
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    );

    // Simulate async behavior (có thể thay bằng API call thực tế)
    return new Promise<typeof allProviderOptions>((resolve) => {
      setTimeout(() => {
        resolve(filteredOptions);
      }, 100);
    });
  }, [allProviderOptions]);

  // Xử lý khi chọn provider
  const handleProviderChange = (providerValue: string | number | string[] | number[]) => {
    const selectedValue = (Array.isArray(providerValue) ? providerValue[0] : providerValue) as string;

    let newConfigData: ModelConfigData;

    if (selectedValue === 'redai') {
      // Chọn RedAI provider mặc định
      newConfigData = {
        ...configData,
        provider: 'redai',
        providerId: undefined,
        modelId: '' // Reset model để user chọn lại từ base models
      };
    } else {
      // Chọn user provider
      const selectedProvider = allProviderOptions.find(option => option.value === selectedValue);

      newConfigData = {
        ...configData,
        provider: selectedProvider?.providerType || TypeProviderEnum.OPENAI,
        providerId: selectedValue,
        modelId: '' // Reset model khi chọn provider mới
      };
    }

    setConfigData(newConfigData);

    if (onSave) {
      onSave(newConfigData);
    }
  };

  return (
    <div
      className="mb-6"
    >
      <div className="p-4 space-y-6">
        {/* Provider selection */}
        <div className="mb-6">
          <AsyncSelect
            label="Provider"
            loadOptions={loadProviderOptions}
            value={configData.provider === 'redai' ? 'redai' : configData.providerId}
            onChange={handleProviderChange}
            placeholder="Chọn provider"
            noOptionsMessage="Không tìm thấy provider nào"
            loadingMessage="Đang tải providers..."
            debounceTime={200}
            fullWidth
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
          <div>
            <label htmlFor="modelId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Model
            </label>
            <Select
              options={modelOptions.length > 0 ? modelOptions : fallbackOptions}
              value={configData.modelId}
              onChange={(value) => handleSelectChange('modelId', value)}
              placeholder="Chọn model"
              disabled={isLoadingModels}
            />
          </div>

          {/* Vector Store - chỉ hiển thị khi model support file_search */}
          {selectedModelConfig?.file_search === true && (
            <div>
              <label htmlFor="vectorStore" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Vector Store
              </label>
              <Select
                options={[
                  { value: 'pinecone', label: 'Pinecone' },
                  { value: 'qdrant', label: 'Qdrant' },
                  { value: 'weaviate', label: 'Weaviate' },
                  { value: 'chroma', label: 'Chroma' }
                ]}
                value={configData.vectorStore}
                onChange={(value) => handleSelectChange('vectorStore', value)}
                placeholder="Chọn vector store"
              />
            </div>
          )}
        </div>

        {/* Instruction textarea */}
        <div className="mb-6">
          <label htmlFor="instruction" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Instruction
          </label>
          <Textarea
            id="instruction"
            name="instruction"
            value={configData.instruction || ''}
            onChange={handleTextareaChange}
            placeholder="Nhập hướng dẫn cho model..."
            className="w-full"
            rows={4}
          />
        </div>

        {/* Advanced configuration checkbox */}
        <div className="mb-4">
          <Checkbox
            label="Tùy chỉnh nâng cao"
            checked={showAdvancedConfig}
            onChange={handleCheckboxChange}
          />
        </div>

        {/* Advanced configuration sliders */}
        {showAdvancedConfig && (
          <div className="space-y-6 border-t pt-4 mt-4">
            {/* Max Tokens - luôn hiển thị */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Max Tokens
              </label>
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Số lượng token tối đa cho mỗi lần gọi API
              </div>
              <Slider
                value={configData.maxTokens}
                min={100}
                max={4096}
                step={1}
                onValueChange={(value: number) => handleSliderChange('maxTokens', value)}
                valueSuffix=""
              />
            </div>

            {/* Temperature - chỉ hiển thị khi model support */}
            {selectedModelConfig?.temperature === true && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Temperature
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Mức độ ngẫu nhiên trong kết quả (0-2)
                </div>
                <Slider
                  value={configData.temperature}
                  min={0}
                  max={2}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('temperature', value)}
                />
              </div>
            )}

            {/* Top P - chỉ hiển thị khi model support */}
            {selectedModelConfig?.top_p === true && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top P
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Xác suất tích lũy cho lựa chọn token (0-1)
                </div>
                <Slider
                  value={configData.topP}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={(value: number) => handleSliderChange('topP', value)}
                />
              </div>
            )}

            {/* Top K - chỉ hiển thị khi model support */}
            {selectedModelConfig?.top_k === true && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top K
                </label>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  Số lượng token có xác suất cao nhất để xem xét
                </div>
                <Slider
                  value={configData.topK}
                  min={1}
                  max={50}
                  step={1}
                  onValueChange={(value: number) => handleSliderChange('topK', value)}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelConfig;
