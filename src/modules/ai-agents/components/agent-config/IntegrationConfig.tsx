import { Button, Icon } from '@/shared/components/common';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import React, { useState, useEffect } from 'react';
import FacebookSlideInForm from './FacebookSlideInForm';
import WebsiteSlideInForm from './WebsiteSlideInForm';
import { useGetFacebookPages } from '@/modules/integration/facebook/hooks/useFacebook';
import { useGetWebsites } from '@/modules/integration/website/hooks/useWebsite';
import { FacebookPageDto } from '@/modules/integration/facebook/types/facebook.types';
import { WebsiteDto } from '@/modules/integration/website/types/website.types';

// Interface cho dữ liệu tích hợp
interface IntegrationItem {
  id: string;
  name: string;
  icon?: string;
  type: 'facebook' | 'website';
  url?: string;
  imageUrl?: string;
  category?: string;
  followers?: number;
  isConnected?: boolean;
  status?: 'active' | 'pending' | 'error';
}

interface IntegrationConfigData {
  integrations: IntegrationItem[];
}

interface IntegrationConfigProps {
  initialData?: IntegrationConfigData;
  onSave?: (data: IntegrationConfigData) => void;
}

/**
 * Component hiển thị một tích hợp (Facebook hoặc Website)
 */
const IntegrationItemCard: React.FC<{
  item: IntegrationItem;
  onRemove: (id: string) => void;
}> = ({ item, onRemove }) => {
  // Xác định màu nền dựa trên loại tích hợp
  const bgColorClass = item.type === 'facebook'
    ? 'bg-blue-50 dark:bg-blue-900/10'
    : 'bg-green-50 dark:bg-green-900/10';

  return (
    <div className={`flex items-center p-3 ${bgColorClass} rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm`}>
      {/* Icon/Avatar */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
        {item.icon ? (
          <img src={item.icon} alt={item.name} className="w-full h-full object-cover" />
        ) : (
          <Icon
            name={item.type === 'facebook' ? 'facebook' : 'globe'}
            size="md"
            className={item.type === 'facebook' ? 'text-blue-600' : 'text-green-600'}
          />
        )}
      </div>

      {/* Thông tin */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{item.name}</h4>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
          <Icon name="document" size="sm" className="mr-1" />
          <span className="truncate">{item.id}</span>
        </div>
      </div>

      {/* Nút xóa */}
      <Button
        variant="ghost"
        size="sm"
        className="ml-2 text-gray-400 hover:text-red-500"
        onClick={() => onRemove(item.id)}
        aria-label="Xóa tích hợp"
      >
        <Icon name="trash" size="sm" />
      </Button>
    </div>
  );
};

/**
 * Component cấu hình tích hợp cho Agent
 */
const IntegrationConfig: React.FC<IntegrationConfigProps> = ({
  initialData,
  onSave
}) => {
  // State cho dữ liệu tích hợp
  const [configData, setConfigData] = useState<IntegrationConfigData>(initialData || {
    integrations: []
  });

  // API hooks để lấy dữ liệu
  const {
    data: facebookPagesResponse,
    isLoading: isLoadingFacebook,
    error: facebookError
  } = useGetFacebookPages({ page: 1, limit: 100 });

  const {
    data: websitesResponse,
    isLoading: isLoadingWebsites,
    error: websitesError
  } = useGetWebsites({ page: 1, limit: 100 });

  // State cho các form slide-in
  const [showFacebookForm, setShowFacebookForm] = useState(false);
  const [showWebsiteForm, setShowWebsiteForm] = useState(false);

  // Cập nhật dữ liệu từ API
  useEffect(() => {
    if (!initialData) {
      const integrations: IntegrationItem[] = [];

      // Thêm Facebook pages từ API
      if (facebookPagesResponse?.result?.items) {
        const facebookIntegrations = facebookPagesResponse.result.items.map((page: FacebookPageDto) => ({
          id: page.facebookPageId,
          name: page.pageName,
          type: 'facebook' as const,
          icon: page.avatarPage || undefined,
          isConnected: page.isActive,
          status: page.isError ? 'error' : (page.isActive ? 'active' : 'pending') as 'active' | 'pending' | 'error'
        }));
        integrations.push(...facebookIntegrations);
      }

      // Thêm Websites từ API
      if (websitesResponse?.result?.items) {
        const websiteIntegrations = websitesResponse.result.items.map((website: WebsiteDto) => ({
          id: website.id,
          name: website.host, // Sử dụng host làm name vì không có websiteName
          type: 'website' as const,
          url: website.host,
          icon: website.logo || undefined,
          isConnected: !!website.agentId, // Có agentId nghĩa là đã kết nối
          status: website.verify ? 'active' : 'pending' as 'active' | 'pending' | 'error'
        }));
        integrations.push(...websiteIntegrations);
      }

      setConfigData({ integrations });
    }
  }, [facebookPagesResponse, websitesResponse, initialData]);

  // Lọc các tích hợp theo loại
  const facebookIntegrations = configData.integrations.filter(item => item.type === 'facebook');
  const websiteIntegrations = configData.integrations.filter(item => item.type === 'website');

  // Xử lý khi xóa một tích hợp
  const handleRemoveIntegration = (id: string) => {
    const updatedIntegrations = configData.integrations.filter(item => item.id !== id);
    const newData = {
      ...configData,
      integrations: updatedIntegrations
    };

    setConfigData(newData);

    if (onSave) {
      onSave(newData);
    }
  };

  // Xử lý khi thêm một tích hợp mới
  const handleAddIntegration = (type: 'facebook' | 'website') => {
    if (type === 'facebook') {
      setShowFacebookForm(true);
    } else {
      setShowWebsiteForm(true);
    }
  };

  // Interface cho Facebook Page từ FacebookSlideInForm
  interface FacebookPage {
    id: string;
    name: string;
    imageUrl?: string;
    category?: string;
    followers?: number;
    isConnected?: boolean;
  }

  // Xử lý khi chọn các trang Facebook
  const handleSelectFacebookPages = (selectedPages: FacebookPage[]) => {
    // Chuyển đổi định dạng dữ liệu
    const newFacebookIntegrations = selectedPages.map(page => ({
      id: page.id,
      name: page.name,
      type: 'facebook' as const,
      imageUrl: page.imageUrl,
      category: page.category,
      followers: page.followers,
      isConnected: page.isConnected
    }));

    // Lọc ra các tích hợp không phải Facebook
    const nonFacebookIntegrations = configData.integrations.filter(
      item => item.type !== 'facebook'
    );

    // Cập nhật dữ liệu
    const newData = {
      ...configData,
      integrations: [...nonFacebookIntegrations, ...newFacebookIntegrations]
    };

    setConfigData(newData);

    if (onSave) {
      onSave(newData);
    }
  };

  // Interface cho Website từ WebsiteSlideInForm
  interface Website {
    id: string;
    name: string;
    url: string;
    icon?: string;
    category?: string;
    isConnected?: boolean;
    status?: 'active' | 'pending' | 'error';
  }

  // Xử lý khi chọn các website
  const handleSelectWebsites = (selectedWebsites: Website[]) => {
    // Chuyển đổi định dạng dữ liệu
    const newWebsiteIntegrations = selectedWebsites.map(website => ({
      id: website.id,
      name: website.name,
      type: 'website' as const,
      url: website.url,
      icon: website.icon,
      category: website.category,
      isConnected: website.isConnected,
      status: website.status
    }));

    // Lọc ra các tích hợp không phải Website
    const nonWebsiteIntegrations = configData.integrations.filter(
      item => item.type !== 'website'
    );

    // Cập nhật dữ liệu
    const newData = {
      ...configData,
      integrations: [...nonWebsiteIntegrations, ...newWebsiteIntegrations]
    };

    setConfigData(newData);

    if (onSave) {
      onSave(newData);
    }
  };

  return (
    <>
      <ConfigComponentWrapper
        componentId="integration"
        title={
          <div className="flex items-center">
            <Icon name="link" size="md" className="mr-2" />
            <span>Tích hợp</span>
          </div>
        }
      >
        <div className="p-4 space-y-6">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Kết nối Agent với các nền tảng
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Kết nối Agent với các trang Facebook và Website để tương tác với người dùng
            </p>
          </div>

          {/* Tích hợp Facebook */}
          <div className="pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                  <Icon name="facebook" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tích hợp Facebook
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddIntegration('facebook')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {isLoadingFacebook ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
                  Đang tải danh sách Facebook Pages...
                </div>
              ) : facebookError ? (
                <div className="text-center py-4 text-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
                  <Icon name="alert-circle" size="md" className="mx-auto mb-2" />
                  Lỗi khi tải danh sách Facebook Pages
                </div>
              ) : facebookIntegrations.length > 0 ? (
                facebookIntegrations.map(item => (
                  <IntegrationItemCard
                    key={item.id}
                    item={item}
                    onRemove={handleRemoveIntegration}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tích hợp Facebook nào
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho Facebook */}
          <FacebookSlideInForm
            isVisible={showFacebookForm}
            onClose={() => setShowFacebookForm(false)}
            onSelect={handleSelectFacebookPages}
            selectedPageIds={facebookIntegrations.map(item => item.id)}
          />

          {/* Tích hợp Website */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center mr-2">
                  <Icon name="globe" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  Tích hợp Website
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddIntegration('website')}
              >
                <Icon name="plus" size="sm" className="mr-1" />
                Thêm
              </Button>
            </div>

            <div className="space-y-3">
              {isLoadingWebsites ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
                  Đang tải danh sách Websites...
                </div>
              ) : websitesError ? (
                <div className="text-center py-4 text-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
                  <Icon name="alert-circle" size="md" className="mx-auto mb-2" />
                  Lỗi khi tải danh sách Websites
                </div>
              ) : websiteIntegrations.length > 0 ? (
                websiteIntegrations.map(item => (
                  <IntegrationItemCard
                    key={item.id}
                    item={item}
                    onRemove={handleRemoveIntegration}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  Chưa có tích hợp Website nào
                </div>
              )}
            </div>
          </div>
          {/* Form slide-in cho Website */}
          <WebsiteSlideInForm
            isVisible={showWebsiteForm}
            onClose={() => setShowWebsiteForm(false)}
            onSelect={handleSelectWebsites}
            selectedWebsiteIds={websiteIntegrations.map(item => item.id)}
          />
        </div>
      </ConfigComponentWrapper>
    </>
  );
};

export default IntegrationConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { IntegrationConfigData, IntegrationConfigProps, IntegrationItem };

