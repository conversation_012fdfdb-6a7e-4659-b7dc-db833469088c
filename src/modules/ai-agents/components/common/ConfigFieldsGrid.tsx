/**
 * Component t<PERSON>i sử dụng cho việc render config fields dạng grid
 */

import React from 'react';
import { Checkbox } from '@/shared/components/common';
import { ConfigFieldItem } from '../../utils/type-agent.utils';
import { TypeAgentConfig } from '../../types/dto';

interface ConfigFieldsGridProps {
  /**
   * <PERSON>h sách các field config
   */
  fields: ConfigFieldItem[];
  
  /**
   * Giá trị config hiện tại
   */
  config: Partial<TypeAgentConfig>;
  
  /**
   * Callback khi thay đổi config
   */
  onChange: (key: keyof TypeAgentConfig) => void;
  
  /**
   * Số cột trong grid (mặc định: 2)
   */
  columns?: 1 | 2 | 3 | 4;
  
  /**
   * Hiển thị description dưới dạng tooltip hay text
   */
  descriptionMode?: 'tooltip' | 'text' | 'none';
  
  /**
   * Class CSS tùy chỉnh
   */
  className?: string;
}

/**
 * Component ConfigFieldsGrid
 */
const ConfigFieldsGrid: React.FC<ConfigFieldsGridProps> = ({
  fields,
  config,
  onChange,
  columns = 2,
  descriptionMode = 'tooltip',
  className = ''
}) => {
  // Tạo class cho grid dựa trên số cột
  const getGridClass = () => {
    const baseClass = 'grid gap-4';
    switch (columns) {
      case 1:
        return `${baseClass} grid-cols-1`;
      case 2:
        return `${baseClass} grid-cols-1 md:grid-cols-2`;
      case 3:
        return `${baseClass} grid-cols-1 md:grid-cols-2 lg:grid-cols-3`;
      case 4:
        return `${baseClass} grid-cols-1 md:grid-cols-2 lg:grid-cols-4`;
      default:
        return `${baseClass} grid-cols-1 md:grid-cols-2`;
    }
  };

  return (
    <div className={`${getGridClass()} ${className}`}>
      {fields.map((field) => (
        <div key={field.key} className="space-y-2">
          <div className="flex items-center">
            <Checkbox
              checked={config[field.key] || false}
              onChange={() => onChange(field.key)}
              id={field.key}
            />
            <label 
              htmlFor={field.key} 
              className="ml-2 block text-sm text-gray-700 dark:text-gray-300 cursor-pointer"
              title={descriptionMode === 'tooltip' ? field.description : undefined}
            >
              {field.label}
            </label>
          </div>
          
          {/* Hiển thị description dưới dạng text */}
          {descriptionMode === 'text' && field.description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 ml-6">
              {field.description}
            </p>
          )}
        </div>
      ))}
    </div>
  );
};

export default ConfigFieldsGrid;
