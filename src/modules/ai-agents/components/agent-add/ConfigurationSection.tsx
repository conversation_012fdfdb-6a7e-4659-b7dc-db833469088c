import React from 'react';
import {
  Card,
  Chip,
  Icon,
  Typography,
  EmptyState,
} from '@/shared/components/common';
import { TypeAgentConfig, ToolDto } from '../../types';
import { TYPE_AGENT_CONFIG_FIELDS } from '../../utils/type-agent.utils';

interface ConfigurationSectionProps {
  /**
   * Cấu hình của type agent
   */
  config: TypeAgentConfig;

  /**
   * Danh sách group tools
   */
  groupTools: ToolDto[];
}

/**
 * Component hiển thị section cấu hình của type agent
 * <PERSON>o gồm config flags và group tools
 */
const ConfigurationSection: React.FC<ConfigurationSectionProps> = ({
  config,
  groupTools,
}) => {
  // const { t } = useTranslation(); // Removed unused variable

  // Tính toán configs được bật và tắt
  const enabledConfigs = TYPE_AGENT_CONFIG_FIELDS.filter(field =>
    config[field.key as keyof TypeAgentConfig]
  );

  const disabledConfigs = TYPE_AGENT_CONFIG_FIELDS.filter(field =>
    !config[field.key as keyof TypeAgentConfig]
  );

  return (
    <div className="space-y-6">

      {/* Configuration Summary */}
      <Card title="Tổng quan cấu hình" variant="bordered">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {enabledConfigs.length}
            </div>
            <div className="text-sm text-green-700 dark:text-green-400">
              Modules bật
            </div>
          </div>

          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <div className="text-2xl font-bold text-gray-600 mb-1">
              {disabledConfigs.length}
            </div>
            <div className="text-sm text-gray-700 dark:text-gray-400">
              Modules tắt
            </div>
          </div>

          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {groupTools.length}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-400">
              Tools
            </div>
          </div>

          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {Math.round((enabledConfigs.length / TYPE_AGENT_CONFIG_FIELDS.length) * 100)}%
            </div>
            <div className="text-sm text-purple-700 dark:text-purple-400">
              Tỷ lệ bật
            </div>
          </div>
        </div>
      </Card>

      {/* Module Configuration */}
      <Card title="Cấu hình Module" variant="bordered">
        <div className="space-y-4">
          {/* Enabled Configs */}
          {enabledConfigs.length > 0 && (
            <div>
              <Typography variant="h6" className="text-green-700 dark:text-green-400 mb-3 flex items-center gap-2">
                <Icon name="check-circle" size="sm" className="text-green-600" />
                Modules được bật ({enabledConfigs.length})
              </Typography>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {enabledConfigs.map((field) => (
                  <div
                    key={field.key}
                    className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
                  >
                    <Icon name="check" size="sm" className="text-green-600 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-green-800 dark:text-green-300">
                        {field.label}
                      </h4>
                      <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                        {field.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Disabled Configs */}
          {disabledConfigs.length > 0 && (
            <div>
              <Typography variant="h6" className="text-gray-500 dark:text-gray-400 mb-3 flex items-center gap-2">
                <Icon name="x-circle" size="sm" className="text-gray-400" />
                Modules bị tắt ({disabledConfigs.length})
              </Typography>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {disabledConfigs.map((field) => (
                  <div
                    key={field.key}
                    className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <Icon name="x" size="sm" className="text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-600 dark:text-gray-400">
                        {field.label}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                        {field.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Tools */}
      <Card title="Tools" variant="bordered">
        {groupTools.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-4">
              <Icon name="tool" size="sm" className="text-blue-600" />
              <Typography variant="h6" className="text-blue-700 dark:text-blue-400">
                Danh sách Tools ({groupTools.length})
              </Typography>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {groupTools.map((tool) => (
                <div
                  key={tool.id}
                  className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                >
                  <Icon name="tool" size="sm" className="text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-blue-800 dark:text-blue-300">
                      {tool.name}
                    </h4>
                    {tool.description && (
                      <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                        {tool.description}
                      </p>
                    )}
                    <div className="mt-2">
                      <Chip variant="info" size="sm">
                        ID: {tool.id}
                      </Chip>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <EmptyState
            icon="tool"
            title="Không có Tools"
            description="Type agent này chưa được cấu hình với bất kỳ tools nào."
            className="py-8"
          />
        )}
      </Card>
    </div>
  );
};

export default ConfigurationSection;
