import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useMemo } from 'react';
import { getBaseModels, getBaseModelsByUserProvider } from '../api/agent.api';
import {
  GetBaseModelsQueryDto,
  BaseModelListResponse,
  BaseModelUserResponseDto,
  TypeProviderEnum,
  BaseModelSortByEnum,
  SortDirection,
} from '../types';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy danh sách base models
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBaseModels = (
  params?: GetBaseModelsQueryDto,
  options?: UseQueryOptions<ApiResponse<BaseModelListResponse>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_LIST, params],
    queryFn: () => getBaseModels(params),
    staleTime: 10 * 60 * 1000, // 10 minutes (base models ít thay đổi)
    ...options,
  });
};

/**
 * Hook để lấy base models theo provider
 * @param providerType Loại provider
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBaseModelsByProvider = (
  providerType: TypeProviderEnum,
  options?: UseQueryOptions<ApiResponse<BaseModelListResponse>>
) => {
  const params: GetBaseModelsQueryDto = {
    provider_type: providerType,
    sortBy: BaseModelSortByEnum.CREATED_AT,
    sortDirection: SortDirection.DESC,
    limit: 100, // Lấy nhiều để có đủ models
  };

  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_LIST, 'by-provider', providerType],
    queryFn: () => getBaseModels(params),
    staleTime: 15 * 60 * 1000, // 15 minutes (longer cache for provider-specific data)
    enabled: !!providerType, // Chỉ chạy khi có providerType
    ...options,
  });
};

/**
 * Hook để lấy tất cả base models cho dropdown selection
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetAllBaseModels = (
  options?: UseQueryOptions<ApiResponse<BaseModelListResponse>>
) => {
  const params: GetBaseModelsQueryDto = {
    sortBy: BaseModelSortByEnum.CREATED_AT,
    sortDirection: SortDirection.DESC,
    limit: 200, // Lấy nhiều để có đủ models
  };

  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_LIST, 'all'],
    queryFn: () => getBaseModels(params),
    staleTime: 20 * 60 * 1000, // 20 minutes (longest cache for all models)
    ...options,
  });
};

/**
 * Utility function để convert BaseModelListItemDto thành Select options
 */
export const convertBaseModelsToSelectOptions = (
  models: BaseModelListResponse['items']
) => {
  return models.map((model) => ({
    value: model.model_id,
    label: `${model.model_id} - ${model.description}`,
    data: model, // Lưu toàn bộ data để sử dụng sau
  }));
};

/**
 * Utility function để group models theo provider
 */
export const groupBaseModelsByProvider = (
  models: BaseModelListResponse['items']
) => {
  return models.reduce((acc, model) => {
    const provider = model.providerType;
    if (!acc[provider]) {
      acc[provider] = [];
    }
    acc[provider].push(model);
    return acc;
  }, {} as Record<TypeProviderEnum, BaseModelListResponse['items']>);
};

/**
 * Utility function để lấy provider name hiển thị
 */
export const getProviderDisplayName = (provider: TypeProviderEnum): string => {
  const providerNames: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.REDAI]: 'RedAI',
    [TypeProviderEnum.OPENAI]: 'OpenAI',
    [TypeProviderEnum.ANTHROPIC]: 'Anthropic',
    [TypeProviderEnum.GOOGLE]: 'Google',
    [TypeProviderEnum.META]: 'Meta',
    [TypeProviderEnum.DEEPSEEK]: 'DeepSeek',
    [TypeProviderEnum.XAI]: 'XAI',
  };

  return providerNames[provider] || provider;
};

/**
 * Utility function để lấy provider icon
 */
export const getProviderIcon = (provider: TypeProviderEnum): string => {
  const providerIcons: Record<TypeProviderEnum, string> = {
    [TypeProviderEnum.REDAI]: 'robot',
    [TypeProviderEnum.OPENAI]: 'openai',
    [TypeProviderEnum.ANTHROPIC]: 'anthropic',
    [TypeProviderEnum.GOOGLE]: 'google',
    [TypeProviderEnum.META]: 'meta',
    [TypeProviderEnum.DEEPSEEK]: 'deepseek',
    [TypeProviderEnum.XAI]: 'grok',
  };

  return providerIcons[provider] || 'robot';
};

/**
 * Type cho config có thể có cả 2 format
 */
type ModelConfigInput = {
  // Old format
  top_p?: boolean;
  top_k?: boolean;
  function?: boolean;
  file_search?: boolean;
  temperature?: boolean;
  response_format?: string[];
  code_interpreter?: boolean;
  reasoning_effort?: string[];

  // New format
  hasText?: boolean;
  hasTopK?: boolean;
  hasTopP?: boolean;
  hasAudio?: boolean;
  hasImage?: boolean;
  hasVideo?: boolean;
  hasFunction?: boolean;
  hasTemperature?: boolean;
  hasReasoningEffort?: string[];
  hasParallelToolCall?: boolean;
} | null | undefined;

/**
 * Utility function để normalize config từ cả 2 format (old và new)
 */
export const normalizeModelConfig = (config: ModelConfigInput) => {
  if (!config) return null;

  return {
    // File search / Vector store - sử dụng file_search hoặc hasFunction làm fallback
    file_search: config.file_search ?? (config.hasFunction !== undefined ? config.hasFunction : true),

    // Temperature
    temperature: config.temperature ?? (config.hasTemperature !== undefined ? config.hasTemperature : true),

    // Top P
    top_p: config.top_p ?? (config.hasTopP !== undefined ? config.hasTopP : true),

    // Top K
    top_k: config.top_k ?? (config.hasTopK !== undefined ? config.hasTopK : true),

    // Function calling
    function: config.function ?? (config.hasFunction !== undefined ? config.hasFunction : true),

    // Other fields
    response_format: config.response_format ?? [],
    code_interpreter: config.code_interpreter ?? false,
    reasoning_effort: config.reasoning_effort ?? config.hasReasoningEffort ?? [],

    // New format fields
    hasText: config.hasText ?? false,
    hasAudio: config.hasAudio ?? false,
    hasImage: config.hasImage ?? false,
    hasVideo: config.hasVideo ?? false,
    hasParallelToolCall: config.hasParallelToolCall ?? false,
  };
};

/**
 * Hook để lấy base models từ user provider cụ thể
 * @param providerId ID của user provider
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBaseModelsByUserProvider = (
  providerId: string,
  options?: UseQueryOptions<ApiResponse<BaseModelUserResponseDto[]>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASE_MODEL_BY_PROVIDER, providerId],
    queryFn: () => getBaseModelsByUserProvider(providerId),
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!providerId, // Chỉ chạy khi có providerId
    ...options,
  });
};

/**
 * Utility function để convert BaseModelUserResponseDto thành Select options
 */
export const convertUserProviderModelsToSelectOptions = (
  models: BaseModelUserResponseDto[]
) => {
  return models.map((model) => ({
    value: model.model_id,
    label: `${model.model_id} - ${model.description}`,
    data: model, // Lưu toàn bộ data để sử dụng sau
  }));
};

/**
 * Hook để lấy danh sách providers bao gồm RedAI mặc định và user providers
 */
export const useGetProvidersForAgentConfig = () => {
  // Import useProviderModels từ integration module
  const { data: userProvidersResponse, isLoading: isLoadingUserProviders } = useQuery({
    queryKey: ['integration-provider-models'],
    queryFn: async () => {
      // Import dynamic để tránh circular dependency
      const { ProviderModelService } = await import('@/modules/integration/services/provider-model.service');
      return ProviderModelService.getProviderModels({ limit: 100 });
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const providers = useMemo(() => {
    const providerList = [];

    // Thêm RedAI provider mặc định
    providerList.push({
      id: 'redai',
      name: 'RedAI',
      type: TypeProviderEnum.REDAI,
      isDefault: true,
      icon: getProviderIcon(TypeProviderEnum.REDAI),
      displayName: getProviderDisplayName(TypeProviderEnum.REDAI),
    });

    // Thêm user providers
    if (userProvidersResponse?.result?.items) {
      userProvidersResponse.result.items.forEach(provider => {
        providerList.push({
          id: provider.id,
          name: provider.name,
          type: provider.type,
          isDefault: false,
          icon: getProviderIcon(provider.type),
          displayName: getProviderDisplayName(provider.type),
        });
      });
    }

    return providerList;
  }, [userProvidersResponse]);

  return {
    providers,
    isLoading: isLoadingUserProviders,
    error: null, // TODO: Add error handling
  };
};

/**
 * Hook để lấy models dựa trên provider được chọn
 * @param providerId ID của provider (hoặc 'redai' cho RedAI mặc định)
 * @param isDefault Có phải là provider mặc định RedAI không
 */
export const useGetModelsByProvider = (
  providerId: string | null,
  isDefault: boolean = false
) => {
  // Cho RedAI mặc định, gọi API base models
  const {
    data: baseModelsResponse,
    isLoading: isLoadingBaseModels,
    error: baseModelsError
  } = useGetAllBaseModels({
    enabled: isDefault && providerId === 'redai',
  } as UseQueryOptions<ApiResponse<BaseModelListResponse>>);

  // Cho user providers, gọi API models theo provider
  const {
    data: userProviderModelsResponse,
    isLoading: isLoadingUserProviderModels,
    error: userProviderModelsError
  } = useGetBaseModelsByUserProvider(providerId as string, {
    enabled: !isDefault && !!providerId,
  } as UseQueryOptions<ApiResponse<BaseModelUserResponseDto[]>>);

  const models = useMemo(() => {
    if (isDefault && baseModelsResponse?.result?.items) {
      return convertBaseModelsToSelectOptions(baseModelsResponse.result.items);
    }

    if (!isDefault && userProviderModelsResponse?.result) {
      return convertUserProviderModelsToSelectOptions(userProviderModelsResponse.result);
    }

    return [];
  }, [isDefault, baseModelsResponse, userProviderModelsResponse]);

  return {
    models,
    isLoading: isDefault ? isLoadingBaseModels : isLoadingUserProviderModels,
    error: isDefault ? baseModelsError : userProviderModelsError,
  };
};
