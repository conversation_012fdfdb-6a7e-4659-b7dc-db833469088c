/**
 * React Hook cho Agent Configuration Layout
 * S<PERSON> dụng XState để quản lý layout state
 */

import { useEffect, useCallback, useMemo, useState } from 'react';
import { useMachine } from '@xstate/react';
import { layoutMachine, LayoutContext } from '../machines/layoutMachine';
import { TypeAgentConfig } from '../types';

/**
 * Breakpoint definitions
 */
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
} as const;

/**
 * Hook để detect responsive breakpoint
 */
const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < BREAKPOINTS.mobile) {
        setBreakpoint('mobile');
      } else if (width < BREAKPOINTS.tablet) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    // Initial check
    updateBreakpoint();

    // Listen for resize
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

/**
 * Hook chính cho Agent Configuration Layout
 */
export const useAgentConfigLayout = (typeAgentConfig: TypeAgentConfig) => {
  const breakpoint = useBreakpoint();

  const [state, send] = useMachine(layoutMachine.provide({
    guards: {},
    actions: {}
  }));

  // Update config khi typeAgentConfig thay đổi
  useEffect(() => {
    send({ type: 'CONFIG_CHANGED', config: typeAgentConfig });
  }, [typeAgentConfig, send]);

  // Update breakpoint khi responsive thay đổi
  useEffect(() => {
    send({ type: 'RESIZE', breakpoint });
  }, [breakpoint, send]);

  // Responsive column ratio dựa trên breakpoint
  const responsiveColumnRatio = useMemo(() => {
    const [leftRatio, rightRatio] = state.context.columnRatio;

    switch (breakpoint) {
      case 'mobile':
        // Mobile: luôn single column
        return [100, 0];
      case 'tablet':
        // Tablet: cân bằng hơn
        if (state.context.layoutMode === 'RIGHT_HEAVY') {
          return [45, 55];
        }
        return [50, 50];
      case 'desktop':
      default:
        return [leftRatio, rightRatio];
    }
  }, [state.context.columnRatio, state.context.layoutMode, breakpoint]);

  // CSS Grid template columns
  const gridTemplateColumns = useMemo(() => {
    const [leftRatio, rightRatio] = responsiveColumnRatio;

    switch (state.context.layoutMode) {
      case 'EMPTY':
        return '1fr';
      case 'LEFT_ONLY':
        return '1fr';
      case 'RIGHT_ONLY':
        return '1fr';
      case 'RIGHT_HEAVY':
      case 'BALANCED':
        if (breakpoint === 'mobile') {
          return '1fr'; // Single column on mobile
        }
        return `${leftRatio}fr ${rightRatio}fr`;
      default:
        return '1fr 1fr';
    }
  }, [state.context.layoutMode, responsiveColumnRatio, breakpoint]);

  // Check if component should be visible
  const isComponentVisible = useCallback((componentName: string): boolean => {
    const allComponents = [
      ...state.context.availableComponents.left,
      ...state.context.availableComponents.right
    ];
    return allComponents.some(comp => comp.name === componentName);
  }, [state.context.availableComponents]);

  // Get component column
  const getComponentColumn = useCallback((componentName: string): 'left' | 'right' | null => {
    if (state.context.availableComponents.left.some(comp => comp.name === componentName)) {
      return 'left';
    }
    if (state.context.availableComponents.right.some(comp => comp.name === componentName)) {
      return 'right';
    }
    return null;
  }, [state.context.availableComponents]);

  // Force layout mode (for testing/debugging)
  const forceLayoutMode = useCallback((mode: LayoutContext['layoutMode']) => {
    send({ type: 'FORCE_LAYOUT', mode });
  }, [send]);

  return {
    // Layout state
    layoutMode: state.context.layoutMode,
    columnRatio: responsiveColumnRatio,
    availableComponents: state.context.availableComponents,
    breakpoint,

    // CSS properties
    gridTemplateColumns,

    // Helper functions
    isComponentVisible,
    getComponentColumn,
    forceLayoutMode,

    // State machine info
    isAnalyzing: state.matches('analyzing'),
    currentState: state.value,

    // Debug info
    debugInfo: {
      originalColumnRatio: state.context.columnRatio,
      responsiveColumnRatio,
      totalLeftComponents: state.context.availableComponents.left.length,
      totalRightComponents: state.context.availableComponents.right.length,
    }
  };
};


