/**
 * Component hiển thị hợp đồng affiliate
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/shared/components/common';
import { ContractAffiliateStepProps } from '../types';
import { PDFViewer } from '@/modules/contract/components';

const ContractDisplay: React.FC<ContractAffiliateStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract-affiliate');

  // Demo contract URL - sử dụng URL hợp đồng affiliate thực tế
  const contractUrl = data.contractUrl || 'https://cdn.redai.vn/contract/HDRULEAffiliate';

  const handleNext = () => {
    onNext({});
  };

  return (
    <div className="w-full">
      {/* PDF Viewer */}
      <div className="mb-8 w-full">
        <PDFViewer
          url={contractUrl}
          base64={data.contractBase64}
          height="700px"
          showDownload={true}
          className="w-full rounded-lg overflow-hidden"
        />
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract-affiliate:actions.previous')}
        </Button>

        <Button
          variant="primary"
          onClick={handleNext}
          isLoading={isLoading}
        >
          {t('contract-affiliate:actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default ContractDisplay;
