import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography, CollapsibleCard, ResponsiveGrid } from '@/shared/components/common';
import { ProductDetail, ProductListItem } from '../types/product.types';
import ProductCard from './ProductCard';
import { useChatPanel } from '@/shared/contexts';

interface RelatedProductsProps {
  /**
   * Sản phẩm chính
   */
  product: ProductDetail;

  /**
   * Trạng thái mở ban đầu
   */
  defaultOpen?: boolean;
}

/**
 * Component hiển thị danh sách sản phẩm liên quan
 */
const RelatedProducts: React.FC<RelatedProductsProps> = ({ product, defaultOpen = true }) => {
  const { t } = useTranslation(['marketplace']);
  const navigate = useNavigate();
  const { isChatPanelOpen } = useChatPanel();

  // Kiểm tra nếu không có sản phẩm liên quan
  if (!product.relatedProducts || product.relatedProducts.length === 0) {
    return null;
  }

  // Xử lý khi click vào category
  const handleCategoryClick = (categorySlug: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/marketplace/category/${categorySlug}`);
  };

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6">{t('product.relatedProducts', 'Sản phẩm liên quan')}</Typography>
      }
      defaultOpen={defaultOpen}
      className="mb-4 p-0"
    >
      <div className="p-4">
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          gap={4}
          className={isChatPanelOpen ? 'chat-panel-open' : ''}
        >
          {product.relatedProducts.map(relatedProduct => (
            <div
              key={relatedProduct.id}
              className="block h-full cursor-pointer"
              onClick={() => navigate(`/marketplace/product/${relatedProduct.id}`)}
            >
              <ProductCard
                product={relatedProduct as ProductListItem}
                onCategoryClick={handleCategoryClick}
              />
            </div>
          ))}
        </ResponsiveGrid>
      </div>
    </CollapsibleCard>
  );
};

export default RelatedProducts;
