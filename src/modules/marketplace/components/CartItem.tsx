import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon, ResponsiveImage, Card, Table } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { CartItem as CartItemType } from '../types/CartItemType';
import { formatPrice } from '../utils/price-formatter';

interface CartItemProps {
  item: CartItemType;
  isSelected: boolean;
  onSelect: (itemId: string, selected: boolean) => void;
  onQuantityChange: (itemId: string, quantity: number) => void;
  onRemove: (itemId: string) => void;
}

/**
 * Component hiển thị một sản phẩm trong giỏ hàng sử dụng Table component
 */
const CartItem: React.FC<CartItemProps> = ({
  item,
  isSelected,
  onSelect,
  onQuantityChange,
  onRemove,
}) => {
  const { t } = useTranslation(['marketplace', 'common']);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>(isSelected ? [item.id] : []);

  // Tính tổng tiền cho sản phẩm
  const totalPrice = item.price * item.quantity;

  // Xử lý khi thay đổi checkbox
  const handleSelectionChange = (keys: React.Key[]) => {
    setSelectedKeys(keys);
    onSelect(item.id, keys.includes(item.id));
  };

  // Xử lý khi tăng số lượng
  const handleIncreaseQuantity = () => {
    onQuantityChange(item.id, item.quantity + 1);
  };

  // Xử lý khi giảm số lượng
  const handleDecreaseQuantity = () => {
    if (item.quantity > 1) {
      onQuantityChange(item.id, item.quantity - 1);
    }
  };

  // Xử lý khi xóa sản phẩm
  const handleRemove = () => {
    onRemove(item.id);
  };

  // Định nghĩa cột cho bảng
  const columns: TableColumn<CartItemType>[] = [
    {
      key: 'product',
      title: t('marketplace:cart.product', 'Sản phẩm'),
      dataIndex: 'name',
      render: (_, record) => (
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 flex-shrink-0 rounded-md overflow-hidden">
            <ResponsiveImage
              src={record.thumbnail}
              alt={record.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1">
            <Typography
              variant="body2"
              className="mb-1 line-clamp-2 h-10 overflow-hidden"
              title={record.name}
            >
              {record.name}
            </Typography>
            {record.function && (
              <Typography variant="caption" color="muted" className="block">
                {record.function}
              </Typography>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      title: t('marketplace:cart.price', 'Giá'),
      dataIndex: 'price',
      align: 'center',
      render: price => (
        <div className="flex items-center justify-center">
          <Typography variant="body2" className="mr-1">
            {formatPrice(price as number)}
          </Typography>
          <Icon name="rpoint" size="md" className="text-red-600" />
        </div>
      ),
    },
    {
      key: 'quantity',
      title: t('marketplace:cart.quantity', 'Số lượng'),
      dataIndex: 'quantity',
      align: 'center',
      render: (_, record) => (
        <div className="flex items-center justify-center">
          <button
            type="button"
            onClick={handleDecreaseQuantity}
            className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 dark:border-gray-600"
            disabled={record.quantity <= 1}
          >
            <Icon name="minus" size="sm" />
          </button>
          <Typography variant="body2" className="mx-3 w-6 text-center">
            {record.quantity}
          </Typography>
          <button
            type="button"
            onClick={handleIncreaseQuantity}
            className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 dark:border-gray-600"
          >
            <Icon name="plus" size="sm" />
          </button>
        </div>
      ),
    },
    {
      key: 'total',
      title: t('marketplace:cart.total', 'Thành tiền'),
      dataIndex: 'price',
      align: 'center',
      render: () => (
        <div className="flex items-center justify-center">
          <Typography variant="body2" className="mr-1">
            {formatPrice(totalPrice)}
          </Typography>
          <Icon name="rpoint" size="md" className="text-red-600" />
        </div>
      ),
    },
    {
      key: 'actions',
      title: t('marketplace:cart.actions', 'Thao tác'),
      align: 'center',
      render: () => (
        <button
          type="button"
          onClick={handleRemove}
          className="text-red-600 hover:text-red-800 dark:hover:text-red-400"
        >
          <Icon name="trash" size="sm" />
        </button>
      ),
    },
  ];

  return (
    <Card className="mb-4">
      <Table<CartItemType>
        data={[item]}
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedKeys,
          onChange: handleSelectionChange,
        }}
        rowKey="id"
        bordered={false}
        hoverable
        size="sm"
      />
    </Card>
  );
};

export default CartItem;
