import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon, Typography } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';

interface QuantityInputProps {
  /**
   * Gi<PERSON> trị hiện tại
   */
  value: number;

  /**
   * Gi<PERSON> trị tối thiểu
   * @default 1
   */
  min?: number;

  /**
   * Giá trị tối đa
   * @default 999
   */
  max?: number;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange: (value: number) => void;

  /**
   * Label hiển thị
   */
  label?: string;

  /**
   * Kích thước của component
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Variant của component
   * @default 'default'
   */
  variant?: 'default' | 'flat' | 'outline';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component cho phép chọn số lượng sản phẩm
 * Thiết kế đơn giản và hiện đại, phù hợp với giao diện marketplace
 */
const QuantityInput: React.FC<QuantityInputProps> = ({
  value,
  min = 1,
  max = 999,
  onChange,
  label,
  size = 'md',
  variant = 'default',
  className = '',
}) => {
  const { t } = useTranslation(['marketplace', 'common']);
  const [quantity, setQuantity] = useState<number>(value);
  useTheme(); // Sử dụng theme

  // Sử dụng label mặc định nếu không được cung cấp
  const displayLabel = label || t('marketplace:product.quantityLabel', 'Số lượng');

  // Cập nhật state khi prop value thay đổi
  useEffect(() => {
    setQuantity(value);
  }, [value]);

  // Xử lý khi giảm số lượng
  const handleDecrease = () => {
    if (quantity > min) {
      const newValue = quantity - 1;
      setQuantity(newValue);
      onChange(newValue);
    }
  };

  // Xử lý khi tăng số lượng
  const handleIncrease = () => {
    if (quantity < max) {
      const newValue = quantity + 1;
      setQuantity(newValue);
      onChange(newValue);
    }
  };

  // Xử lý khi nhập trực tiếp
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value, 10);

    if (isNaN(value)) {
      setQuantity(min);
      onChange(min);
      return;
    }

    // Giới hạn giá trị trong khoảng min-max
    const newValue = Math.max(min, Math.min(max, value));
    setQuantity(newValue);
    onChange(newValue);
  };

  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
  }[size];

  const buttonSizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-9 h-10',
    lg: 'w-10 h-12',
  }[size];

  const iconSizeMap = {
    sm: 'xs',
    md: 'sm',
    lg: 'md',
  };

  const iconSize = iconSizeMap[size] as 'xs' | 'sm' | 'md';

  const inputWidthClasses = {
    sm: 'w-8',
    md: 'w-10',
    lg: 'w-12',
  }[size];

  // Xác định variant classes
  const variantClasses = {
    default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm',
    flat: 'bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
    outline: 'bg-transparent border border-gray-300 dark:border-gray-600',
  }[variant];

  return (
    <div className={`flex items-center ${className}`}>
      {displayLabel && (
        <Typography
          variant="body2"
          className="mr-3 text-gray-700 dark:text-gray-300 whitespace-nowrap"
        >
          {displayLabel}
        </Typography>
      )}

      <div
        className={`inline-flex items-center rounded-md overflow-hidden ${variantClasses} ${sizeClasses}`}
      >
        <button
          type="button"
          onClick={handleDecrease}
          disabled={quantity <= min}
          className={`${buttonSizeClasses} flex items-center justify-center text-gray-400 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed`}
          aria-label={t('marketplace:product.quantityDecrease', 'Giảm số lượng')}
        >
          <Icon name="minus" size={iconSize} />
        </button>

        <div
          className={`${inputWidthClasses} flex items-center justify-center border-x border-gray-200 dark:border-gray-700`}
        >
          <input
            type="text"
            value={quantity}
            onChange={handleChange}
            className="w-full h-full text-center bg-transparent border-0 focus:ring-0 focus:outline-none text-gray-800 dark:text-gray-200 text-sm"
            aria-label={t('marketplace:product.quantityInput', 'Nhập số lượng')}
          />
        </div>

        <button
          type="button"
          onClick={handleIncrease}
          disabled={quantity >= max}
          className={`${buttonSizeClasses} flex items-center justify-center text-gray-400 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed`}
          aria-label={t('marketplace:product.quantityIncrease', 'Tăng số lượng')}
        >
          <Icon name="plus" size={iconSize} />
        </button>
      </div>
    </div>
  );
};

export default QuantityInput;
