/**
 * Utility functions để fetch nội dung từ URL
 */

/**
 * Fetch HTML content từ URL
 */
export const fetchHtmlContent = async (url: string): Promise<string> => {
  try {
    console.log('🔍 [fetchHtmlContent] Fetching content from URL:', url);

    // Kiểm tra xem có phải URL từ CDN RedAI không
    const isRedAiCdn = url.includes('cdn.redai.vn') || url.includes('redai.vn');

    if (!isRedAiCdn) {
      console.warn('⚠️ [fetchHtmlContent] URL không phải từ CDN RedAI, sẽ hiển thị link thay thế');
      return `<div class="external-content-warning">
        <h4>📄 Nội dung bên ngoài</h4>
        <p>Nội dung này được lưu trữ bên ngoài hệ thống:</p>
        <a href="${url}" target="_blank" rel="noopener noreferrer" class="external-link">
          🔗 Xem nội dung tại đây
        </a>
      </div>`;
    }

    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors', // Explicitly set CORS mode
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const content = await response.text();
    console.log('✅ [fetchHtmlContent] Content fetched successfully, length:', content.length);

    return content;
  } catch (error) {
    console.error('❌ [fetchHtmlContent] Error fetching content:', error);

    // Nếu lỗi CORS hoặc network, hiển thị link thay thế
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      return `<div class="cors-error-fallback">
        <h4>🔒 Không thể tải nội dung trực tiếp</h4>
        <p>Do hạn chế bảo mật, nội dung không thể hiển thị trực tiếp. Vui lòng:</p>
        <div class="fallback-actions">
          <a href="${url}" target="_blank" rel="noopener noreferrer" class="btn btn-primary" style="display: inline-block; padding: 8px 16px; background: #dc2626; color: white; text-decoration: none; border-radius: 4px; margin-right: 8px;">
            🔗 Xem nội dung
          </a>
          <a href="${url}" download class="btn btn-secondary" style="display: inline-block; padding: 8px 16px; background: #6b7280; color: white; text-decoration: none; border-radius: 4px;">
            ⬇️ Tải xuống
          </a>
        </div>
      </div>`;
    }

    return `<div class="error-message">
      <h4>❌ Lỗi tải nội dung</h4>
      <p>Không thể tải nội dung từ: ${url}</p>
      <p>Lỗi: ${error instanceof Error ? error.message : 'Unknown error'}</p>
      <a href="${url}" target="_blank" rel="noopener noreferrer">🔗 Thử xem trực tiếp</a>
    </div>`;
  }
};

/**
 * Fetch và parse nội dung PDF/document (hiển thị link download)
 */
export const fetchDocumentContent = async (url: string): Promise<string> => {
  try {
    console.log('🔍 [fetchDocumentContent] Processing document URL:', url);

    // Kiểm tra xem có phải PDF không
    if (url.toLowerCase().includes('.pdf')) {
      return `<div class="document-viewer">
        <div class="document-info">
          <h4>📄 Tài liệu PDF</h4>
          <p>Tài liệu này có thể được xem hoặc tải xuống:</p>
        </div>
        <div class="document-actions">
          <a href="${url}" target="_blank" class="btn btn-primary" style="display: inline-block; padding: 8px 16px; background: #dc2626; color: white; text-decoration: none; border-radius: 4px; margin-right: 8px;">
            🔗 Xem tài liệu
          </a>
          <a href="${url}" download class="btn btn-secondary" style="display: inline-block; padding: 8px 16px; background: #6b7280; color: white; text-decoration: none; border-radius: 4px;">
            ⬇️ Tải xuống
          </a>
        </div>
        <div class="document-embed" style="margin-top: 16px;">
          <iframe src="${url}" width="100%" height="600px" style="border: 1px solid #e5e7eb; border-radius: 4px;">
            <p>Trình duyệt của bạn không hỗ trợ hiển thị PDF. <a href="${url}" target="_blank">Nhấp vào đây để xem</a></p>
          </iframe>
        </div>
      </div>`;
    }

    // Nếu không phải PDF, thử fetch như HTML
    return await fetchHtmlContent(url);
  } catch (error) {
    console.error('❌ [fetchDocumentContent] Error processing document:', error);
    return `<div class="error-message">
      <p>Không thể tải tài liệu từ: ${url}</p>
      <p><a href="${url}" target="_blank">Nhấp vào đây để xem trực tiếp</a></p>
    </div>`;
  }
};

/**
 * Sanitize HTML content để đảm bảo an toàn
 */
export const sanitizeHtmlContent = (html: string): string => {
  // Basic sanitization - trong production nên sử dụng thư viện như DOMPurify
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/<iframe[^>]*(?:(?!<\/iframe>)[\s\S])*<\/iframe>/gi, (match) => {
      // Chỉ cho phép iframe từ domain tin cậy
      if (match.includes('cdn.redai.vn') || match.includes('redai.vn')) {
        return match;
      }
      return '<!-- Iframe removed for security -->';
    });
};

/**
 * Check if URL is accessible
 */
export const checkUrlAccessibility = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};
