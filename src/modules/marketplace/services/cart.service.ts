/**
 * Service for cart API
 */

import { apiClient } from '@/shared/api';
import {
  ApiCart,
  AddToCartDto,
  UpdateCartItemDto,
  RemoveBatchCartItemsDto,
} from './marketplace-api.service';

/**
 * Base URL for cart API
 */
const BASE_URL = '/user/marketplace/cart';

/**
 * Cart service
 */
export const CartService = {
  /**
   * Get current cart
   */
  getCart: async (): Promise<ApiCart> => {
    try {
      const response = await apiClient.get<ApiCart>(BASE_URL);
      return response.result;
    } catch (error) {
      console.error('Error fetching cart:', error);
      throw error;
    }
  },

  /**
   * Add product to cart
   */
  addToCart: async (data: AddToCartDto): Promise<ApiCart> => {
    try {
      const response = await apiClient.post<ApiCart>(BASE_URL, data);
      return response.result;
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  },

  /**
   * Update cart item quantity
   */
  updateCartItem: async (cartItemId: number, data: UpdateCartItemDto): Promise<ApiCart> => {
    try {
      const response = await apiClient.put<ApiCart>(`${BASE_URL}/${cartItemId}`, data);
      return response.result;
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  },

  /**
   * Remove product from cart
   */
  removeCartItem: async (cartItemId: number): Promise<ApiCart> => {
    try {
      const response = await apiClient.delete<ApiCart>(`${BASE_URL}/${cartItemId}`);
      return response.result;
    } catch (error) {
      console.error('Error removing cart item:', error);
      throw error;
    }
  },

  /**
   * Remove multiple cart items
   */
  removeBatchCartItems: async (data: RemoveBatchCartItemsDto): Promise<ApiCart> => {
    try {
      const response = await apiClient.delete<ApiCart>(`${BASE_URL}/batch`, { data });
      return response.result;
    } catch (error) {
      console.error('Error removing batch cart items:', error);
      throw error;
    }
  },
};
