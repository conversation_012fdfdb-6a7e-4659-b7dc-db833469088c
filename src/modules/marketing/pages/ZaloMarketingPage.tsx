import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Trang redirect từ ZaloMarketingPage cũ sang ZaloOverviewPage mới
 */
const ZaloMarketingPage: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to the new Zalo overview page
    navigate('/marketing/zalo/overview', { replace: true });
  }, [navigate]);

  return null;
};

export default ZaloMarketingPage;