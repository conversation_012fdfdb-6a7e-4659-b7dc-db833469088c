import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Badge,
  Modal,
  Progress,
  Tooltip,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';

interface FacebookAccountCardProps {
  /**
   * Facebook Ad Account data
   */
  account: {
    id: string;
    accountId: string;
    name: string;
    businessName?: string;
    status: 'active' | 'inactive' | 'error';
    currency: string;
    timezone: string;
    balance?: number;
    spendCap?: number;
    accountStatus?: number;
  };
  
  /**
   * Show detailed view
   */
  showDetails?: boolean;
  
  /**
   * Callback khi sync account
   */
  onSync?: (accountId: string) => void;
  
  /**
   * Callback khi edit account
   */
  onEdit?: (accountId: string) => void;
  
  /**
   * Callback khi delete account
   */
  onDelete?: (accountId: string) => void;
  
  /**
   * Loading state
   */
  isLoading?: boolean;
}

/**
 * Facebook Account Card Component
 * Hiển thị thông tin tài khoản Facebook Ads dưới dạng card
 */
const FacebookAccountCard: React.FC<FacebookAccountCardProps> = ({
  account,
  showDetails = false,
  onSync,
  onEdit,
  onDelete,
  isLoading = false,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const { user } = useFacebookAuth();

  const getStatusConfig = (status: string) => {
    const configs = {
      active: {
        label: t('common:status.active', 'Hoạt động'),
        variant: 'success' as const,
        icon: 'check-circle',
      },
      inactive: {
        label: t('common:status.inactive', 'Không hoạt động'),
        variant: 'secondary' as const,
        icon: 'pause-circle',
      },
      error: {
        label: t('common:status.error', 'Lỗi'),
        variant: 'danger' as const,
        icon: 'alert-circle',
      },
    };
    return configs[status as keyof typeof configs] || configs.inactive;
  };

  const statusConfig = getStatusConfig(account.status);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: account.currency,
    }).format(amount);
  };

  const getSpendPercentage = () => {
    if (!account.balance || !account.spendCap) return 0;
    const spent = account.spendCap - account.balance;
    return Math.min((spent / account.spendCap) * 100, 100);
  };

  const handleSync = () => {
    onSync?.(account.id);
  };

  const handleEdit = () => {
    onEdit?.(account.id);
  };

  const handleDelete = () => {
    onDelete?.(account.id);
  };

  return (
    <>
      <Card className={`transition-all duration-200 hover:shadow-md ${isLoading ? 'opacity-50' : ''}`}>
        <div className="p-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="facebook" className="text-blue-600" />
                <Typography variant="h6" className="font-semibold">
                  {account.name}
                </Typography>
                <Badge variant={statusConfig.variant}>
                  <Icon name={statusConfig.icon} size="xs" className="mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>
              
              <Typography variant="caption" className="text-muted-foreground">
                {account.accountId}
              </Typography>
              
              {account.businessName && (
                <Typography variant="body2" className="text-muted-foreground mt-1">
                  {account.businessName}
                </Typography>
              )}
            </div>

            {/* Actions */}
            <div className="flex space-x-1">
              <Tooltip content={t('marketing:facebookAds.accounts.actions.sync', 'Đồng bộ')}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSync}
                  disabled={isLoading}
                >
                  <Icon name="refresh-cw" size="sm" />
                </Button>
              </Tooltip>
              
              <Tooltip content={t('marketing:facebookAds.accounts.actions.edit', 'Chỉnh sửa')}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEdit}
                  disabled={isLoading}
                >
                  <Icon name="edit" size="sm" />
                </Button>
              </Tooltip>
              
              <Tooltip content={t('marketing:facebookAds.accounts.actions.delete', 'Xóa')}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isLoading}
                  className="text-destructive hover:text-destructive"
                >
                  <Icon name="trash-2" size="sm" />
                </Button>
              </Tooltip>
            </div>
          </div>

          {/* Account Info */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:facebookAds.accounts.currency', 'Tiền tệ')}:
              </Typography>
              <Typography variant="caption" className="font-medium">
                {account.currency}
              </Typography>
            </div>
            
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:facebookAds.accounts.timezone', 'Múi giờ')}:
              </Typography>
              <Typography variant="caption" className="font-medium">
                {account.timezone}
              </Typography>
            </div>

            {/* Budget Info */}
            {account.balance !== undefined && account.spendCap !== undefined && (
              <div className="space-y-2 pt-2 border-t">
                <div className="flex justify-between items-center">
                  <Typography variant="caption" className="text-muted-foreground">
                    {t('marketing:facebookAds.accounts.balance', 'Số dư')}:
                  </Typography>
                  <Typography variant="caption" className="font-medium">
                    {formatCurrency(account.balance)}
                  </Typography>
                </div>
                
                <div className="flex justify-between items-center">
                  <Typography variant="caption" className="text-muted-foreground">
                    {t('marketing:facebookAds.accounts.spendCap', 'Hạn mức')}:
                  </Typography>
                  <Typography variant="caption" className="font-medium">
                    {formatCurrency(account.spendCap)}
                  </Typography>
                </div>

                {/* Spend Progress */}
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('marketing:facebookAds.accounts.spendProgress', 'Đã chi tiêu')}:
                    </Typography>
                    <Typography variant="caption" className="font-medium">
                      {getSpendPercentage().toFixed(1)}%
                    </Typography>
                  </div>
                  <Progress value={getSpendPercentage()} className="h-1" />
                </div>
              </div>
            )}
          </div>

          {/* View Details Button */}
          {showDetails && (
            <div className="pt-3 border-t mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDetailsModal(true)}
                className="w-full"
              >
                <Icon name="eye" className="mr-2" />
                {t('marketing:facebookAds.accounts.viewDetails', 'Xem chi tiết')}
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title={t('marketing:facebookAds.accounts.details.title', 'Chi tiết tài khoản')}
        size="md"
      >
        <div className="space-y-4">
          {/* Account Info */}
          <Card variant="default" className="p-4">
            <Typography variant="h6" className="mb-3">
              {t('marketing:facebookAds.accounts.details.accountInfo', 'Thông tin tài khoản')}
            </Typography>
            
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.accounts.name', 'Tên tài khoản')}:
                </Typography>
                <Typography variant="body2" className="font-medium">
                  {account.name}
                </Typography>
              </div>
              
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  ID:
                </Typography>
                <Typography variant="body2" className="font-medium font-mono">
                  {account.accountId}
                </Typography>
              </div>
              
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.accounts.business', 'Doanh nghiệp')}:
                </Typography>
                <Typography variant="body2" className="font-medium">
                  {account.businessName || 'N/A'}
                </Typography>
              </div>
              
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.accounts.status', 'Trạng thái')}:
                </Typography>
                <Badge variant={statusConfig.variant} className="mt-1">
                  <Icon name={statusConfig.icon} size="xs" className="mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>
            </div>
          </Card>

          {/* Connected User */}
          {user && (
            <Card variant="default" className="p-4">
              <Typography variant="h6" className="mb-3">
                {t('marketing:facebookAds.accounts.details.connectedUser', 'Người dùng kết nối')}
              </Typography>
              
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Icon name="user" className="text-blue-600" />
                </div>
                <div>
                  <Typography variant="body2" className="font-medium">
                    {user.name}
                  </Typography>
                  {user.email && (
                    <Typography variant="caption" className="text-muted-foreground">
                      {user.email}
                    </Typography>
                  )}
                </div>
              </div>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDetailsModal(false)}
            >
              {t('common:button.close', 'Đóng')}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default FacebookAccountCard;
