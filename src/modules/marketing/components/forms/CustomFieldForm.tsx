import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Card,
  Textarea,
  ConditionalField,
  DatePicker,
  Checkbox,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { z } from 'zod';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail } from '../../services/custom-field.service';
import { CreateCustomFieldRequest, UpdateCustomFieldRequest, CustomFieldType } from '../../types/custom-field.types';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
}

// Pattern suggestions cho validation
const PATTERN_SUGGESTIONS = [
  { key: 'email', value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
  { key: 'phoneVN', value: '^(\\+84|0)[0-9]{9,10}$' },
  { key: 'phoneIntl', value: '^\\+[1-9]\\d{1,14}$' },
  { key: 'postalCodeVN', value: '^[0-9]{5,6}$' },
  { key: 'lettersOnly', value: '^[a-zA-ZÀ-ỹ\\s]+$' },
  { key: 'numbersOnly', value: '^[0-9]+$' },
  { key: 'alphanumeric', value: '^[a-zA-Z0-9]+$' },
  { key: 'noSpecialChars', value: '^[a-zA-Z0-9\\s]+$' },
  { key: 'url', value: '^https?://(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b(?:[-a-zA-Z0-9()@:%_+.~#?&//=]*)$' },
  { key: 'ipv4', value: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$' },
  { key: 'strongPassword', value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$' },
  { key: 'vietnameseName', value: '^[a-zA-ZÀ-ỹ\\s]+$' },
  { key: 'studentId', value: '^[A-Z]{2}[0-9]{6}$' },
  { key: 'nationalId', value: '^[0-9]{9,12}$' },
  { key: 'taxCode', value: '^[0-9]{10,13}$' },
  { key: 'dateFormat', value: '^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[012])\\/(19|20)\\d\\d$' },
  { key: 'timeFormat', value: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
  { key: 'hexColor', value: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$' },
  { key: 'base64', value: '^[A-Za-z0-9+\\/]*={0,2}$' },
  { key: 'uuid', value: '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' },
  { key: 'filename', value: '^[^<>:"/\\\\|?*]+$' },
  { key: 'urlSlug', value: '^[a-z0-9]+(?:-[a-z0-9]+)*$' },
  { key: 'variableName', value: '^[a-zA-Z_$][a-zA-Z0-9_$]*$' },
  { key: 'creditCard', value: '^[0-9]{13,19}$' },
  { key: 'qrCode', value: '^[A-Za-z0-9\\-._~:/?#\\[\\]@!$&\'()*+,;=]+$' },
  { key: 'gpsCoordinate', value: '^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}$' },
  { key: 'rgbColor', value: '^rgb\\(([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\)$' },
  { key: 'domain', value: '^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$' },
  { key: 'decimal', value: '^\\d+(\\.\\d{1,2})?$' },
  { key: 'barcode', value: '^[0-9]{8,14}$' },
];

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  initialData,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { mutateAsync: createCustomField } = useCreateCustomField();
  const { mutateAsync: updateCustomField } = useUpdateCustomField();

  const formRef = useRef<FormRef<Record<string, unknown>> | null>(null);
  const patternInputRef = useRef<HTMLInputElement>(null);

  // State cho advanced settings
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Schema cho form
  const customFieldSchema = z.object({
    id: z.string().min(1, t('marketing:customField.form.idRequired')),
    displayName: z.string().min(1, t('marketing:customField.form.displayNameRequired')),
    label: z.string().optional(), // Nhãn không bắt buộc
    type: z.string().min(1, t('marketing:customField.form.typeRequired')),
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    id: '',
    displayName: '',
    type: 'text',
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    try {
      if (initialData) {
        // Cập nhật trường tùy chỉnh - sử dụng UpdateCustomFieldRequest
        const updateData: UpdateCustomFieldRequest = {
          fieldKey: String(values.id),
          displayName: String(values.displayName),
          dataType: String(values.type) as CustomFieldType,
          description: values.description ? String(values.description) : undefined,
        };

        await updateCustomField({
          id: initialData.id,
          data: updateData,
        });
      } else {
        // Tạo trường tùy chỉnh mới - sử dụng CreateCustomFieldRequest
        const createData: CreateCustomFieldRequest = {
          fieldKey: String(values.id),
          displayName: String(values.displayName),
          dataType: String(values.type) as CustomFieldType,
          description: values.description ? String(values.description) : undefined,
        };

        await createCustomField(createData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.validation as Record<string, unknown> | undefined;

    return {
      id: initialData.configId || '', // ✅ Sử dụng configId từ API response
      displayName: initialData.configJson?.displayName as string || initialData.label || '',
      type: initialData.type,
      placeholder: initialData.configJson?.placeholder as string || '',
      defaultValue: initialData.configJson?.defaultValue as string || '',
      description: initialData.configJson?.description as string || '',
      validation: {
        minLength: validation?.minLength ? String(validation.minLength) : '',
        maxLength: validation?.maxLength ? String(validation.maxLength) : '',
        pattern: validation?.pattern ? String(validation.pattern) : '',
      },
      options: initialData.configJson?.options
        ? JSON.stringify(initialData.configJson.options)
        : '',
    };
  };

  return (
    <Card title={initialData ? t('marketing:customField.edit') : t('marketing:customField.add')}>
      <Form
        ref={formRef}
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-6"
      >
        {/* Thông tin cơ bản */}
        <div className="space-y-4 mb-6">
          <FormItem
            name="type"
            label={t('marketing:customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('marketing:customField.types.text') },
                { value: 'number', label: t('marketing:customField.types.number') },
                { value: 'boolean', label: t('marketing:customField.types.boolean') },
                { value: 'date', label: t('marketing:customField.types.date') },
                { value: 'select', label: t('marketing:customField.types.select') },
                { value: 'object', label: t('marketing:customField.types.object') },
                { value: 'array', label: t('marketing:customField.types.array') },
              ]}
            />
          </FormItem>

          <FormItem
            name="id"
            label={t('marketing:customField.form.fieldIdLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('marketing:customField.form.fieldIdPlaceholder')}
              pattern="^[a-zA-Z0-9_-]+$"
            />
          </FormItem>

          <FormItem
            name="displayName"
            label={t('marketing:customField.form.displayNameLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('marketing:customField.form.displayNamePlaceholder')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('marketing:customField.form.description')}
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('marketing:customField.form.descriptionPlaceholder')}
            />
          </FormItem>

          {/* Tùy chọn cho kiểu dữ liệu select */}
          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.EQUALS,
              value: 'select',
            }}
          >
            <FormItem
              name="options"
              label={t('marketing:customField.form.options')}
            >
              <Textarea
                fullWidth
                rows={6}
                placeholder={t('marketing:customField.form.selectOptionsPlaceholder')}
              />
            </FormItem>
          </ConditionalField>
        </div>

        {/* Checkbox để hiện/ẩn cài đặt nâng cao */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            checked={showAdvancedSettings}
            onChange={setShowAdvancedSettings}
            label={t('marketing:customField.form.showAdvancedSettings', 'Hiển thị cài đặt nâng cao')}
          />
        </div>

        {/* Cài đặt nâng cao */}
        {showAdvancedSettings && (
          <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <FormItem
              name="placeholder"
              label={t('marketing:customField.form.placeholder')}
            >
              <Input fullWidth placeholder={t('marketing:customField.form.placeholderPlaceholder')} />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'number',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  type="number"
                  placeholder={t('marketing:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'boolean',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <Select
                  fullWidth
                  placeholder={t('marketing:customField.form.booleanDefaultPlaceholder')}
                  options={[
                    { value: 'true', label: t('marketing:customField.booleanValues.true') },
                    { value: 'false', label: t('marketing:customField.booleanValues.false') },
                  ]}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'date',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <DatePicker
                  fullWidth
                  placeholder={t('marketing:customField.form.dateDefaultPlaceholder')}
                  format="dd/MM/yyyy"
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.IN,
                value: ['text', 'select', 'object', 'array'],
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  placeholder={t('marketing:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            {/* Validation Pattern với suggestions */}
            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'text',
              }}
            >
              <div className="space-y-2">
                <FormItem
                  name="validation.pattern"
                  label={t('marketing:customField.form.pattern')}
                >
                  <Input
                    ref={patternInputRef}
                    fullWidth
                    placeholder="^[A-Za-z0-9]+$"
                  />
                </FormItem>

                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p className="font-medium mb-2">{t('marketing:customField.form.patternSuggestions')}</p>
                  <div className="grid grid-cols-2 gap-2">
                    {PATTERN_SUGGESTIONS.slice(0, 8).map((pattern) => (
                      <button
                        key={pattern.key}
                        type="button"
                        className="text-left p-2 text-xs bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600"
                        onClick={() => {
                          if (patternInputRef.current) {
                            patternInputRef.current.value = pattern.value;
                            // Trigger change event
                            const event = new Event('input', { bubbles: true });
                            patternInputRef.current.dispatchEvent(event);
                          }
                        }}
                      >
                        <div className="font-medium text-blue-600 dark:text-blue-400">
                          {t(`marketing:customField.patterns.${pattern.key}`)}
                        </div>
                        <div className="text-gray-500 dark:text-gray-400 truncate">
                          {pattern.value}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </ConditionalField>

            {/* Validation cho number */}
            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'number',
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                  name="validation.min"
                  label={t('marketing:customField.form.min')}
                >
                  <Input fullWidth type="number" placeholder="0" />
                </FormItem>
                <FormItem
                  name="validation.max"
                  label={t('marketing:customField.form.max')}
                >
                  <Input fullWidth type="number" placeholder="100" />
                </FormItem>
              </div>
            </ConditionalField>

            {/* Validation cho text length */}
            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.IN,
                value: ['text', 'string'],
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                  name="validation.minLength"
                  label={t('marketing:customField.form.minLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="0" />
                </FormItem>
                <FormItem
                  name="validation.maxLength"
                  label={t('marketing:customField.form.maxLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="255" />
                </FormItem>
              </div>
            </ConditionalField>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
