/**
 * Types cho tích hợp Facebook Ads
 */

/**
 * Trạng thái tài khoản Facebook Ads
 */
export enum FacebookAdsAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  ERROR = 'error',
}

/**
 * Loại mục tiêu chiến dịch Facebook Ads
 */
export enum FacebookAdsCampaignObjective {
  BRAND_AWARENESS = 'BRAND_AWARENESS',
  REACH = 'REACH',
  TRAFFIC = 'TRAFFIC',
  APP_INSTALLS = 'APP_INSTALLS',
  VIDEO_VIEWS = 'VIDEO_VIEWS',
  LEAD_GENERATION = 'LEAD_GENERATION',
  MESSAGES = 'MESSAGES',
  CONVERSIONS = 'CONVERSIONS',
  CATALOG_SALES = 'CATALOG_SALES',
  STORE_TRAFFIC = 'STORE_TRAFFIC',
}

/**
 * Trạng thái chiến dịch Facebook Ads
 */
export enum FacebookAdsCampaignStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
  ARCHIVED = 'ARCHIVED',
}

/**
 * Loại định dạng quảng cáo Facebook
 */
export enum FacebookAdsFormat {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  CAROUSEL = 'CAROUSEL',
  COLLECTION = 'COLLECTION',
  SLIDESHOW = 'SLIDESHOW',
}

/**
 * DTO tài khoản Facebook Ads
 */
export interface FacebookAdsAccountDto {
  id: number;
  userId: number;
  accountId: string;
  name: string;
  accessToken: string;
  status: FacebookAdsAccountStatus;
  currency: string;
  timezone: string;
  businessName?: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO chiến dịch Facebook Ads
 */
export interface FacebookAdsCampaignDto {
  id: number;
  userId: number;
  accountId: number;
  campaignId: string;
  name: string;
  status: FacebookAdsCampaignStatus;
  objective: FacebookAdsCampaignObjective;
  spendCap?: number;
  dailyBudget?: number;
  lifetimeBudget?: number;
  startTime?: number;
  endTime?: number;
  userCampaignId?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tập quảng cáo Facebook Ads
 */
export interface FacebookAdsAdSetDto {
  id: number;
  userId: number;
  campaignId: number;
  adSetId: string;
  name: string;
  status: string;
  dailyBudget?: number;
  lifetimeBudget?: number;
  bidStrategy: string;
  billingEvent: string;
  optimization: string;
  targeting: Record<string, unknown>;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO quảng cáo Facebook Ads
 */
export interface FacebookAdsAdDto {
  id: number;
  userId: number;
  adSetId: number;
  adId: string;
  name: string;
  status: string;
  format: FacebookAdsFormat;
  creativeId?: string;
  headline?: string;
  body?: string;
  imageUrl?: string;
  videoUrl?: string;
  linkUrl?: string;
  callToAction?: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO hiệu suất Facebook Ads
 */
export interface FacebookAdsInsightDto {
  id: number;
  userId: number;
  accountId: number;
  campaignId?: number;
  adSetId?: number;
  adId?: number;
  date: string;
  impressions: number;
  clicks: number;
  spend: number;
  ctr: number;
  cpc: number;
  reach: number;
  frequency: number;
  costPerResult: number;
  conversions: number;
  conversionValue: number;
  createdAt: number;
}

/**
 * DTO tham số truy vấn tài khoản Facebook Ads
 */
export interface FacebookAdsAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: FacebookAdsAccountStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn chiến dịch Facebook Ads
 */
export interface FacebookAdsCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  accountId?: string;
  status?: FacebookAdsCampaignStatus;
  objective?: FacebookAdsCampaignObjective;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn hiệu suất Facebook Ads
 */
export interface FacebookAdsInsightQueryDto {
  accountId: number;
  campaignId?: number;
  adSetId?: number;
  adId?: number;
  startDate: string;
  endDate: string;
  granularity?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

/**
 * DTO tạo tài khoản Facebook Ads
 */
export interface CreateFacebookAdsAccountDto {
  accountId: string;
  accessToken: string;
  name: string;
}

/**
 * DTO cập nhật tài khoản Facebook Ads
 */
export interface UpdateFacebookAdsAccountDto {
  name?: string;
  accessToken?: string;
  status?: FacebookAdsAccountStatus;
}

/**
 * DTO tạo chiến dịch Facebook Ads
 */
export interface CreateFacebookAdsCampaignDto {
  accountId: number;
  name: string;
  objective: FacebookAdsCampaignObjective;
  status?: FacebookAdsCampaignStatus;
  spendCap?: number;
  dailyBudget?: number;
  lifetimeBudget?: number;
  startTime?: number;
  endTime?: number;
  userCampaignId?: number;
}

/**
 * DTO cập nhật chiến dịch Facebook Ads
 */
export interface UpdateFacebookAdsCampaignDto {
  name?: string;
  status?: FacebookAdsCampaignStatus;
  spendCap?: number;
  dailyBudget?: number;
  lifetimeBudget?: number;
  startTime?: number;
  endTime?: number;
}

/**
 * DTO phản hồi paging
 */
export interface PagingResponseDto<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
} 