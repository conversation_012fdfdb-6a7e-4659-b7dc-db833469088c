/**
 * Provider Model API Client
 */

import { apiClient } from '@/shared/api';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  ProviderModel,
  ProviderModelListItem,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  ProviderModelQueryDto,
} from '../types/provider-model.types';

const API_BASE_URL = 'key-llm';

/**
 * API client cho provider model operations
 */
export class ProviderModelApi {
  /**
   * Lấy danh sách provider models với phân trang
   */
  static async getProviderModels(
    params: Partial<ProviderModelQueryDto> = {}
  ): Promise<ApiResponseDto<PaginatedResult<ProviderModelListItem>>> {
    try {
      return await apiClient.get<PaginatedResult<ProviderModelListItem>>(
        API_BASE_URL,
        { params }
      );
    } catch (error) {
      console.error('Error fetching provider models:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON>y thông tin chi tiết một provider model
   */
  static async getProviderModel(id: string): Promise<ApiResponseDto<ProviderModel>> {
    try {
      return await apiClient.get<ProviderModel>(
        `${API_BASE_URL}/${id}`
      );
    } catch (error) {
      console.error(`Error fetching provider model ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo provider model mới
   */
  static async createProviderModel(
    data: CreateProviderModelDto
  ): Promise<ApiResponseDto<ProviderModel>> {
    try {
      return await apiClient.post<ProviderModel>(
        API_BASE_URL,
        data
      );
    } catch (error) {
      console.error('Error creating provider model:', error);
      throw error;
    }
  }

  /**
   * Cập nhật provider model
   */
  static async updateProviderModel(
    id: string,
    data: UpdateProviderModelDto
  ): Promise<ApiResponseDto<ProviderModel>> {
    try {
      return await apiClient.patch<ProviderModel>(
        `${API_BASE_URL}/${id}`,
        data
      );
    } catch (error) {
      console.error(`Error updating provider model ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa provider model
   */
  static async deleteProviderModel(id: string): Promise<ApiResponseDto<void>> {
    try {
      return await apiClient.delete<void>(
        `${API_BASE_URL}/${id}`
      );
    } catch (error) {
      console.error(`Error deleting provider model ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa nhiều provider models cùng lúc
   */
  static async deleteMultipleProviderModels(ids: string[]): Promise<ApiResponseDto<void>> {
    try {
      return await apiClient.delete<void>(API_BASE_URL, {
        data: { ids }
      });
    } catch (error) {
      console.error('Error deleting multiple provider models:', error);
      throw error;
    }
  }

  /**
   * Kiểm tra tính khả dụng của API key
   */
  static async validateApiKey(
    type: string,
    apiKey: string
  ): Promise<ApiResponseDto<{ valid: boolean; message?: string }>> {
    try {
      return await apiClient.post<{ valid: boolean; message?: string }>(
        `${API_BASE_URL}/validate-api-key`,
        { type, apiKey }
      );
    } catch (error) {
      console.error('Error validating API key:', error);
      throw error;
    }
  }
}
