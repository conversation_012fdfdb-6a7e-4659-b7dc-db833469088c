import { z } from 'zod';

/**
 * Database Types Schema
 */
export const DATABASE_TYPES = {
  MYSQL: 'mysql',
  POSTGRESQL: 'postgresql',
  MONGODB: 'mongodb',
  REDIS: 'redis',
  SQLITE: 'sqlite',
} as const;

/**
 * Database Type Schema
 */
export const databaseTypeSchema = z.enum([
  DATABASE_TYPES.MYSQL,
  DATABASE_TYPES.POSTGRESQL,
  DATABASE_TYPES.MONGODB,
  DATABASE_TYPES.REDIS,
  DATABASE_TYPES.SQLITE,
]);

/**
 * Database Connection Status Schema
 */
export const databaseConnectionStatusSchema = z.enum([
  'active',
  'inactive', 
  'error',
  'testing',
  'pending'
]);

/**
 * Base Database Credentials Schema
 */
const baseDatabaseCredentialsSchema = z.object({
  host: z.string().optional(),
  port: z.number().min(1).max(65535).optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  database: z.string().optional(),
  schema: z.string().optional(),
  connectionString: z.string().optional(),
  collection: z.string().optional(),
  databaseIndex: z.number().min(0).max(15).optional(),
  filePath: z.string().optional(),
  mode: z.enum(['readonly', 'readwrite', 'create']).optional(),
  ssl: z.boolean().optional(),
  sslCert: z.string().optional(),
  sslKey: z.string().optional(),
  sslCa: z.string().optional(),
});

/**
 * MySQL Credentials Schema
 */
export const mysqlCredentialsSchema = baseDatabaseCredentialsSchema.extend({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535).default(3306),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  database: z.string().min(1, 'Database name is required'),
});

/**
 * PostgreSQL Credentials Schema
 */
export const postgresqlCredentialsSchema = baseDatabaseCredentialsSchema.extend({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535).default(5432),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  database: z.string().min(1, 'Database name is required'),
  schema: z.string().optional(),
});

/**
 * MongoDB Credentials Schema
 */
export const mongodbCredentialsSchema = baseDatabaseCredentialsSchema.extend({
  connectionString: z.string().min(1, 'Connection string is required'),
  database: z.string().min(1, 'Database name is required'),
  collection: z.string().optional(),
});

/**
 * Redis Credentials Schema
 */
export const redisCredentialsSchema = baseDatabaseCredentialsSchema.extend({
  host: z.string().min(1, 'Host is required'),
  port: z.number().min(1).max(65535).default(6379),
  password: z.string().optional(),
  databaseIndex: z.number().min(0).max(15).default(0),
});

/**
 * SQLite Credentials Schema
 */
export const sqliteCredentialsSchema = baseDatabaseCredentialsSchema.extend({
  filePath: z.string().min(1, 'File path is required'),
  mode: z.enum(['readonly', 'readwrite', 'create']).default('readwrite'),
});

/**
 * Database Retry Config Schema
 */
export const databaseRetryConfigSchema = z.object({
  maxRetries: z.number().min(0).max(10).default(3),
  retryDelay: z.number().min(100).max(30000).default(1000),
  backoffMultiplier: z.number().min(1).max(5).default(2),
});

/**
 * Database Pool Config Schema
 */
export const databasePoolConfigSchema = z.object({
  min: z.number().min(0).max(100).default(2),
  max: z.number().min(1).max(100).default(10),
  acquireTimeoutMillis: z.number().min(1000).max(60000).default(30000),
  idleTimeoutMillis: z.number().min(1000).max(600000).default(300000),
});

/**
 * Database Settings Schema
 */
export const databaseSettingsSchema = z.object({
  connectionTimeout: z.number().min(1000).max(60000).default(30000),
  queryTimeout: z.number().min(1000).max(300000).default(60000),
  maxConnections: z.number().min(1).max(100).default(10),
  retryConfig: databaseRetryConfigSchema.default({}),
  poolConfig: databasePoolConfigSchema.default({}),
  enableLogging: z.boolean().default(false),
  timezone: z.string().default('UTC'),
});

/**
 * Database Test Result Schema
 */
export const databaseTestResultSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  responseTime: z.number().min(0),
  timestamp: z.string(),
  details: z.object({
    version: z.string().optional(),
    serverInfo: z.string().optional(),
    error: z.string().optional(),
  }).optional(),
});

/**
 * Database Connection Form Schema
 */
export const databaseConnectionFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  type: databaseTypeSchema,
  displayName: z.string().min(1, 'Display name is required').max(255, 'Display name too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  credentials: baseDatabaseCredentialsSchema,
  settings: databaseSettingsSchema.partial().default({}),
  isDefault: z.boolean().default(false),
});

/**
 * Database Connection Query Schema
 */
export const databaseConnectionQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  status: databaseConnectionStatusSchema.optional(),
  type: databaseTypeSchema.optional(),
  search: z.string().max(100).optional(),
  sortBy: z.enum(['name', 'type', 'status', 'createdAt', 'updatedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * Database Test Request Schema
 */
export const databaseTestRequestSchema = z.object({
  connectionId: z.string().min(1, 'Connection ID is required'),
  testQuery: z.string().optional(),
});

/**
 * Dynamic Credentials Validation based on Database Type
 */
export const validateCredentialsByType = (type: string, credentials: unknown) => {
  switch (type) {
    case DATABASE_TYPES.MYSQL:
      return mysqlCredentialsSchema.parse(credentials);
    case DATABASE_TYPES.POSTGRESQL:
      return postgresqlCredentialsSchema.parse(credentials);
    case DATABASE_TYPES.MONGODB:
      return mongodbCredentialsSchema.parse(credentials);
    case DATABASE_TYPES.REDIS:
      return redisCredentialsSchema.parse(credentials);
    case DATABASE_TYPES.SQLITE:
      return sqliteCredentialsSchema.parse(credentials);
    default:
      throw new Error(`Unsupported database type: ${type}`);
  }
};
