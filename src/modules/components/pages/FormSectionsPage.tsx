import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../components';
import {
  Form,
  FormItem,
  FormGrid,
  Input,
  Button,
  Toggle,
  Icon,
  Card,
} from '@/shared/components/common';
import FormSection from '@/shared/components/common/Form/FormSection';
import { z } from 'zod';
import { FormRef } from '@/shared/components/common/Form/Form';

// Định nghĩa schema validation với Zod
const userSchema = z.object({
  // Thông tin cá nhân
  firstName: z.string().min(1, 'Họ là bắt buộc'),
  lastName: z.string().min(1, 'Tên là bắt buộc'),
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
  phone: z.string().optional(),

  // Thông tin công ty
  companyName: z.string().optional(),
  jobTitle: z.string().optional(),
  department: z.string().optional(),

  // Thông tin địa chỉ
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),

  // Thông tin thanh toán
  cardNumber: z.string().optional(),
  cardName: z.string().optional(),
  cardExpiry: z.string().optional(),
  cardCvv: z.string().optional(),
  saveCard: z.boolean().optional(),

  // Cài đặt tài khoản
  receiveNewsletter: z.boolean().optional(),
  receivePromotions: z.boolean().optional(),
  allowDataCollection: z.boolean().optional(),
});

// Định nghĩa kiểu dữ liệu từ schema
type UserFormValues = z.infer<typeof userSchema>;

// Schema đơn giản cho các form demo
const demoSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email'),
  companyName: z.string().optional(),
  receiveNewsletter: z.boolean().optional(),
});

const FormSectionsPage: React.FC = () => {
  const { t } = useTranslation();
  const formRef = useRef<FormRef<UserFormValues>>(null);
  const [formData, setFormData] = useState<UserFormValues | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    personal: true,
    company: true,
    address: true,
    payment: true,
    settings: true,
    basic: true,
    collapsible: true,
    grid: true,
    styled: true,
    variants: true,
    sizes: true,
    animation: true,
    accordion1: true,
    accordion2: false,
    accordion3: false,
    badge: true,
    iconPosition: true,
  });

  // Xử lý submit form
  const handleSubmit = (values: UserFormValues) => {
    console.log('Form submitted:', values);
    setFormData(values);
  };

  // Xử lý thay đổi trạng thái đóng/mở của section
  const handleSectionExpandChange = (section: string, expanded: boolean): void => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: expanded,
    }));
  };

  // Xử lý đóng tất cả các section
  const collapseAll = () => {
    const newState = Object.keys(expandedSections).reduce(
      (acc, key) => {
        acc[key] = false;
        return acc;
      },
      {} as Record<string, boolean>
    );
    setExpandedSections(newState);
  };

  // Xử lý mở tất cả các section
  const expandAll = () => {
    const newState = Object.keys(expandedSections).reduce(
      (acc, key) => {
        acc[key] = true;
        return acc;
      },
      {} as Record<string, boolean>
    );
    setExpandedSections(newState);
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.formSections.title')}
        </h1>
        <p className="text-muted mb-4">{t('components.formSections.description')}</p>
        <div className="flex space-x-4 mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={expandAll}
            leftIcon={<Icon name="chevron-down" size="sm" />}
          >
            {t('components.formSections.expandAll')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={collapseAll}
            leftIcon={<Icon name="chevron-up" size="sm" />}
          >
            {t('components.formSections.collapseAll')}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.title')}
            description={t('components.formSections.description')}
            code={`import { Form, FormItem, FormSection, Input, Button } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Form schema={schema} onSubmit={handleSubmit}>
  <FormSection
    title={t('components.formSections.personalInfo')}
    description={t('components.formSections.personalInfoDesc')}
  >
    <FormItem name="firstName" label={t('components.formSections.fields.firstName')} required>
      <Input placeholder={t('components.formSections.placeholders.firstName')} />
    </FormItem>
    <FormItem name="lastName" label={t('components.formSections.fields.lastName')} required>
      <Input placeholder={t('components.formSections.placeholders.lastName')} />
    </FormItem>
    <FormItem name="email" label={t('components.formSections.fields.email')} required>
      <Input type="email" placeholder={t('components.formSections.placeholders.email')} />
    </FormItem>
  </FormSection>

  <Button type="submit">{t('components.formSections.buttons.submit')}</Button>
</Form>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <FormSection
                  title={t('components.formSections.personalInfo')}
                  description={t('components.formSections.personalInfoDesc')}
                  collapsible
                  defaultExpanded={expandedSections.basic}
                  onExpandChange={expanded => handleSectionExpandChange('basic', expanded)}
                >
                  <div className="space-y-4">
                    <FormItem
                      name="firstName"
                      label={t('components.formSections.fields.firstName')}
                      required
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.firstName')}
                        fullWidth
                      />
                    </FormItem>
                    <FormItem
                      name="lastName"
                      label={t('components.formSections.fields.lastName')}
                      required
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.lastName')}
                        fullWidth
                      />
                    </FormItem>
                    <FormItem
                      name="email"
                      label={t('components.formSections.fields.email')}
                      required
                    >
                      <Input
                        type="email"
                        placeholder={t('components.formSections.placeholders.email')}
                        fullWidth
                      />
                    </FormItem>
                  </div>
                </FormSection>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.formSectionComponent')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.formSectionDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.features')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>{t('components.formSections.featuresList.groupFields')}</li>
                  <li>{t('components.formSections.featuresList.collapsible')}</li>
                  <li>{t('components.formSections.featuresList.titleDesc')}</li>
                  <li>{t('components.formSections.featuresList.variants')}</li>
                  <li>{t('components.formSections.featuresList.sizes')}</li>
                  <li>{t('components.formSections.featuresList.animation')}</li>
                  <li>{t('components.formSections.featuresList.accordion')}</li>
                  <li>{t('components.formSections.featuresList.badge')}</li>
                  <li>{t('components.formSections.featuresList.iconPosition')}</li>
                  <li>{t('components.formSections.featuresList.customStyle')}</li>
                  <li>{t('components.formSections.featuresList.darkMode')}</li>
                  <li>{t('components.formSections.featuresList.responsive')}</li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">{t('components.formSections.props')}</h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <code>title</code>: {t('components.formSections.propsList.title')}
                  </li>
                  <li>
                    <code>description</code>: {t('components.formSections.propsList.description')}
                  </li>
                  <li>
                    <code>collapsible</code>: {t('components.formSections.propsList.collapsible')}
                  </li>
                  <li>
                    <code>defaultExpanded</code>:{' '}
                    {t('components.formSections.propsList.defaultExpanded')}
                  </li>
                  <li>
                    <code>variant</code>: {t('components.formSections.propsList.variant')}
                  </li>
                  <li>
                    <code>size</code>: {t('components.formSections.propsList.size')}
                  </li>
                  <li>
                    <code>icon</code>: {t('components.formSections.propsList.icon')}
                  </li>
                  <li>
                    <code>iconPosition</code>: {t('components.formSections.propsList.iconPosition')}
                  </li>
                  <li>
                    <code>badge</code>: {t('components.formSections.propsList.badge')}
                  </li>
                  <li>
                    <code>animated</code>: {t('components.formSections.propsList.animated')}
                  </li>
                  <li>
                    <code>animationType</code>:{' '}
                    {t('components.formSections.propsList.animationType')}
                  </li>
                  <li>
                    <code>animationDuration</code>:{' '}
                    {t('components.formSections.propsList.animationDuration')}
                  </li>
                  <li>
                    <code>accordionId</code>: {t('components.formSections.propsList.accordionId')}
                  </li>
                  <li>
                    <code>id</code>: {t('components.formSections.propsList.id')}
                  </li>
                  <li>
                    <code>onExpandChange</code>:{' '}
                    {t('components.formSections.propsList.onExpandChange')}
                  </li>
                  <li>
                    <code>className</code>: {t('components.formSections.propsList.className')}
                  </li>
                  <li>
                    <code>titleClassName</code>:{' '}
                    {t('components.formSections.propsList.titleClassName')}
                  </li>
                  <li>
                    <code>descriptionClassName</code>:{' '}
                    {t('components.formSections.propsList.descriptionClassName')}
                  </li>
                  <li>
                    <code>contentClassName</code>:{' '}
                    {t('components.formSections.propsList.contentClassName')}
                  </li>
                  <li>
                    <code>headerClassName</code>:{' '}
                    {t('components.formSections.propsList.headerClassName')}
                  </li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.collapsible.title')}
            description={t('components.formSections.collapsible.description')}
            code={`import { Form, FormItem, FormSection, Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

const { t } = useTranslation();

// State to track expanded sections
const [expandedSections, setExpandedSections] = useState({
  personal: true,
  company: false,
});

// Handle section expand/collapse
const handleSectionExpandChange = (section, expanded) => {
  setExpandedSections(prev => ({
    ...prev,
    [section]: expanded,
  }));
};

<Form schema={schema} onSubmit={handleSubmit}>
  <FormSection
    title={t('components.formSections.personalInfo')}
    description={t('components.formSections.personalInfoDesc')}
    collapsible
    defaultExpanded={expandedSections.personal}
    onExpandChange={(expanded) => handleSectionExpandChange('personal', expanded)}
  >
    {/* Form fields */}
  </FormSection>

  <FormSection
    title={t('components.formSections.companyInfo')}
    description={t('components.formSections.companyInfoDesc')}
    collapsible
    defaultExpanded={expandedSections.company}
    onExpandChange={(expanded) => handleSectionExpandChange('company', expanded)}
  >
    {/* Form fields */}
  </FormSection>
</Form>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <div className="space-y-4">
                  <FormSection
                    title={t('components.formSections.personalInfo')}
                    description={t('components.formSections.personalInfoDesc')}
                    collapsible
                    defaultExpanded={expandedSections.collapsible}
                    onExpandChange={expanded => handleSectionExpandChange('collapsible', expanded)}
                  >
                    <div className="space-y-4">
                      <FormItem
                        name="firstName"
                        label={t('components.formSections.fields.firstName')}
                        required
                      >
                        <Input
                          placeholder={t('components.formSections.placeholders.firstName')}
                          fullWidth
                        />
                      </FormItem>
                    </div>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.companyInfo')}
                    description={t('components.formSections.companyInfoDesc')}
                    collapsible
                    defaultExpanded={false}
                  >
                    <div className="space-y-4">
                      <FormItem
                        name="companyName"
                        label={t('components.formSections.fields.companyName')}
                      >
                        <Input
                          placeholder={t('components.formSections.placeholders.companyName')}
                          fullWidth
                        />
                      </FormItem>
                    </div>
                  </FormSection>
                </div>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.collapsibleSections')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.collapsibleDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">{t('components.formSections.usage')}</h3>
                <ol className="list-decimal list-inside space-y-1">
                  <li>{t('components.formSections.usageSteps.addProp')}</li>
                  <li>{t('components.formSections.usageSteps.defaultState')}</li>
                  <li>{t('components.formSections.usageSteps.trackChanges')}</li>
                </ol>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.managingSections')}
                </h3>
                <p>{t('components.formSections.managingSectionsDesc')}</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>{t('components.formSections.managingStepsList.stateObject')}</li>
                  <li>{t('components.formSections.managingStepsList.functions')}</li>
                  <li>{t('components.formSections.managingStepsList.callback')}</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.nestedSections.title')}
            description={t('components.formSections.nestedSections.description')}
            code={`import { Form, FormItem, FormSection, FormGrid, Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Form schema={schema} onSubmit={handleSubmit}>
  <FormSection
    title={t('components.formSections.personalInfo')}
    description={t('components.formSections.personalInfoDesc')}
  >
    <FormGrid columns={2} gap="md">
      <FormItem name="firstName" label={t('components.formSections.fields.firstName')} required>
        <Input placeholder={t('components.formSections.placeholders.firstName')} />
      </FormItem>
      <FormItem name="lastName" label={t('components.formSections.fields.lastName')} required>
        <Input placeholder={t('components.formSections.placeholders.lastName')} />
      </FormItem>
      <FormItem name="email" label={t('components.formSections.fields.email')} required className="col-span-2">
        <Input type="email" placeholder={t('components.formSections.placeholders.email')} />
      </FormItem>
    </FormGrid>
  </FormSection>
</Form>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <FormSection
                  title={t('components.formSections.personalInfo')}
                  description={t('components.formSections.personalInfoDesc')}
                  collapsible
                  defaultExpanded={expandedSections.grid}
                  onExpandChange={expanded => handleSectionExpandChange('grid', expanded)}
                >
                  <FormGrid columns={2} gap="md">
                    <FormItem
                      name="firstName"
                      label={t('components.formSections.fields.firstName')}
                      required
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.firstName')}
                        fullWidth
                      />
                    </FormItem>
                    <FormItem
                      name="lastName"
                      label={t('components.formSections.fields.lastName')}
                      required
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.lastName')}
                        fullWidth
                      />
                    </FormItem>
                    <FormItem
                      name="email"
                      label={t('components.formSections.fields.email')}
                      required
                      className="col-span-2"
                    >
                      <Input
                        type="email"
                        placeholder={t('components.formSections.placeholders.email')}
                        fullWidth
                      />
                    </FormItem>
                  </FormGrid>
                </FormSection>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.gridIntegration')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.gridIntegrationDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.advantages')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>{t('components.formSections.advantagesList.gridLayout')}</li>
                  <li>{t('components.formSections.advantagesList.responsive')}</li>
                  <li>{t('components.formSections.advantagesList.columns')}</li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.gridUsage')}
                </h3>
                <ol className="list-decimal list-inside space-y-1">
                  <li>{t('components.formSections.gridUsageSteps.placeGrid')}</li>
                  <li>{t('components.formSections.gridUsageSteps.setColumns')}</li>
                  <li>{t('components.formSections.gridUsageSteps.spanColumns')}</li>
                </ol>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.customStyling.title')}
            description={t('components.formSections.customStyling.description')}
            code={`import { Form, FormItem, FormSection, Input } from '@/shared/components/common';

<FormSection
  title="Newsletter Subscription"
  description="Manage your newsletter preferences"
  className="border-2 border-primary"
  titleClassName="text-primary"
  descriptionClassName="italic"
  contentClassName="bg-gray-50 dark:bg-gray-800"
>
  <FormItem name="receiveNewsletter" label="Receive Newsletter" inline>
    <Toggle />
  </FormItem>
</FormSection>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <FormSection
                  title={t('components.formSections.newsletterSubscription')}
                  description={t('components.formSections.managePreferences')}
                  className="border-2 border-primary"
                  titleClassName="text-primary"
                  descriptionClassName="italic"
                  contentClassName="bg-gray-50 dark:bg-gray-800"
                  collapsible
                  defaultExpanded={expandedSections.styled}
                  onExpandChange={expanded => handleSectionExpandChange('styled', expanded)}
                >
                  <FormItem
                    name="receiveNewsletter"
                    label={t('components.formSections.fields.receiveNewsletter')}
                    inline
                  >
                    <Toggle />
                  </FormItem>
                </FormSection>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.customStylingTitle')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.customStylingDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.stylingProps')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <code>className</code>:{' '}
                    {t('components.formSections.stylingPropsList.className')}
                  </li>
                  <li>
                    <code>titleClassName</code>:{' '}
                    {t('components.formSections.stylingPropsList.titleClassName')}
                  </li>
                  <li>
                    <code>descriptionClassName</code>:{' '}
                    {t('components.formSections.stylingPropsList.descriptionClassName')}
                  </li>
                  <li>
                    <code>contentClassName</code>:{' '}
                    {t('components.formSections.stylingPropsList.contentClassName')}
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.examples')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>{t('components.formSections.examplesList.primaryBorder')}</li>
                  <li>{t('components.formSections.examplesList.titleColor')}</li>
                  <li>{t('components.formSections.examplesList.italicDesc')}</li>
                  <li>{t('components.formSections.examplesList.contentBg')}</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Variants Demo */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.variants.title')}
            description={t('components.formSections.variants.description')}
            code={`import { Form, FormItem, FormSection, Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<FormSection
  title={t('components.formSections.variants.default')}
  description={t('components.formSections.variants.defaultDesc')}
  variant="default"
  collapsible
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.variants.bordered')}
  description={t('components.formSections.variants.borderedDesc')}
  variant="bordered"
  collapsible
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.variants.elevated')}
  description={t('components.formSections.variants.elevatedDesc')}
  variant="elevated"
  collapsible
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.variants.gradient')}
  description={t('components.formSections.variants.gradientDesc')}
  variant="gradient"
  collapsible
>
  {/* Form fields */}
</FormSection>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <div className="space-y-4">
                  <FormSection
                    title={t('components.formSections.variants.default')}
                    description={t('components.formSections.variants.defaultDesc')}
                    variant="default"
                    collapsible
                    defaultExpanded={expandedSections.variants}
                    onExpandChange={expanded => handleSectionExpandChange('variants', expanded)}
                  >
                    <FormItem
                      name="firstName"
                      label={t('components.formSections.fields.firstName')}
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.firstName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.variants.bordered')}
                    description={t('components.formSections.variants.borderedDesc')}
                    variant="bordered"
                    collapsible
                  >
                    <FormItem name="lastName" label={t('components.formSections.fields.lastName')}>
                      <Input
                        placeholder={t('components.formSections.placeholders.lastName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.variants.elevated')}
                    description={t('components.formSections.variants.elevatedDesc')}
                    variant="elevated"
                    collapsible
                  >
                    <FormItem name="email" label={t('components.formSections.fields.email')}>
                      <Input
                        type="email"
                        placeholder={t('components.formSections.placeholders.email')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.variants.gradient')}
                    description={t('components.formSections.variants.gradientDesc')}
                    variant="gradient"
                    collapsible
                  >
                    <FormItem
                      name="companyName"
                      label={t('components.formSections.fields.companyName')}
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.companyName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>
                </div>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.variants.title')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.variantsDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.variantsTitle')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <code>default</code>: {t('components.formSections.variantsList.default')}
                  </li>
                  <li>
                    <code>bordered</code>: {t('components.formSections.variantsList.bordered')}
                  </li>
                  <li>
                    <code>elevated</code>: {t('components.formSections.variantsList.elevated')}
                  </li>
                  <li>
                    <code>gradient</code>: {t('components.formSections.variantsList.gradient')}
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.variantUsage')}
                </h3>
                <p>{t('components.formSections.usage')}:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2">
                  {`<FormSection
  variant="elevated"
  title={t('components.formSections.variants.elevated')}
  description={t('components.formSections.variants.elevatedDesc')}
>
  {/* Content */}
</FormSection>`}
                </pre>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Sizes Demo */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.sizes.title')}
            description={t('components.formSections.sizes.description')}
            code={`import { Form, FormItem, FormSection, Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<FormSection
  title={t('components.formSections.sizes.small')}
  description={t('components.formSections.sizes.smallDesc')}
  size="sm"
  collapsible
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.sizes.medium')}
  description={t('components.formSections.sizes.mediumDesc')}
  size="md"
  collapsible
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.sizes.large')}
  description={t('components.formSections.sizes.largeDesc')}
  size="lg"
  collapsible
>
  {/* Form fields */}
</FormSection>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <div className="space-y-4">
                  <FormSection
                    title={t('components.formSections.sizes.small')}
                    description={t('components.formSections.sizes.smallDesc')}
                    size="sm"
                    collapsible
                    defaultExpanded={expandedSections.sizes}
                    onExpandChange={expanded => handleSectionExpandChange('sizes', expanded)}
                  >
                    <FormItem
                      name="firstName"
                      label={t('components.formSections.fields.firstName')}
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.firstName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.sizes.medium')}
                    description={t('components.formSections.sizes.mediumDesc')}
                    size="md"
                    collapsible
                  >
                    <FormItem name="lastName" label={t('components.formSections.fields.lastName')}>
                      <Input
                        placeholder={t('components.formSections.placeholders.lastName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.sizes.large')}
                    description={t('components.formSections.sizes.largeDesc')}
                    size="lg"
                    collapsible
                  >
                    <FormItem name="email" label={t('components.formSections.fields.email')}>
                      <Input
                        type="email"
                        placeholder={t('components.formSections.placeholders.email')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>
                </div>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.sizes.title')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.sizesDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.sizesTitle')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <code>sm</code>: {t('components.formSections.sizesList.small')}
                  </li>
                  <li>
                    <code>md</code>: {t('components.formSections.sizesList.medium')}
                  </li>
                  <li>
                    <code>lg</code>: {t('components.formSections.sizesList.large')}
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">{t('components.formSections.usage')}</h3>
                <p>{t('components.formSections.sizeUsage')}:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2">
                  {`<FormSection
  size="lg"
  title={t('components.formSections.sizes.large')}
  description={t('components.formSections.sizes.largeDesc')}
>
  {/* Content */}
</FormSection>`}
                </pre>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Animation Demo */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.animation.title')}
            description={t('components.formSections.animation.description')}
            code={`import { Form, FormItem, FormSection, Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<FormSection
  title={t('components.formSections.animation.fade')}
  description={t('components.formSections.animation.fadeDesc')}
  collapsible
  animated={true}
  animationType="fade"
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.animation.slide')}
  description={t('components.formSections.animation.slideDesc')}
  collapsible
  animated={true}
  animationType="slide"
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.animation.both')}
  description={t('components.formSections.animation.bothDesc')}
  collapsible
  animated={true}
  animationType="both"
>
  {/* Form fields */}
</FormSection>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <div className="space-y-4">
                  <FormSection
                    title={t('components.formSections.animation.fade')}
                    description={t('components.formSections.animation.fadeDesc')}
                    collapsible
                    animated={true}
                    animationType="fade"
                    defaultExpanded={expandedSections.animation}
                    onExpandChange={expanded => handleSectionExpandChange('animation', expanded)}
                  >
                    <FormItem
                      name="firstName"
                      label={t('components.formSections.fields.firstName')}
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.firstName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.animation.slide')}
                    description={t('components.formSections.animation.slideDesc')}
                    collapsible
                    animated={true}
                    animationType="slide"
                  >
                    <FormItem name="lastName" label={t('components.formSections.fields.lastName')}>
                      <Input
                        placeholder={t('components.formSections.placeholders.lastName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.animation.both')}
                    description={t('components.formSections.animation.bothDesc')}
                    collapsible
                    animated={true}
                    animationType="both"
                  >
                    <FormItem name="email" label={t('components.formSections.fields.email')}>
                      <Input
                        type="email"
                        placeholder={t('components.formSections.placeholders.email')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>
                </div>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.animation.title')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.animationDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.animationTypes')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <code>fade</code>: {t('components.formSections.animationTypesList.fade')}
                  </li>
                  <li>
                    <code>slide</code>: {t('components.formSections.animationTypesList.slide')}
                  </li>
                  <li>
                    <code>both</code>: {t('components.formSections.animationTypesList.both')}
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">{t('components.formSections.usage')}</h3>
                <p>{t('components.formSections.animationUsage')}:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2">
                  {`<FormSection
  collapsible
  animated={true}
  animationType="both"
  animationDuration={500}
  title={t('components.formSections.animation.both')}
  description={t('components.formSections.animation.bothDesc')}
>
  {/* Content */}
</FormSection>`}
                </pre>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Accordion Demo */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.accordion.title')}
            description={t('components.formSections.accordion.description')}
            code={`import { Form, FormItem, FormSection, Input } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<Form schema={schema} onSubmit={handleSubmit}>
  <FormSection
    title={\`\${t('components.formSections.accordion.group1')} - Section 1\`}
    description={t('components.formSections.accordion.firstSection')}
    collapsible
    accordionId="personal-info"
    id="section1"
    defaultExpanded={true}
  >
    {/* Form fields */}
  </FormSection>

  <FormSection
    title={\`\${t('components.formSections.accordion.group1')} - Section 2\`}
    description={t('components.formSections.accordion.secondSection')}
    collapsible
    accordionId="personal-info"
    id="section2"
  >
    {/* Form fields */}
  </FormSection>

  <FormSection
    title={\`\${t('components.formSections.accordion.group1')} - Section 3\`}
    description={t('components.formSections.accordion.thirdSection')}
    collapsible
    accordionId="personal-info"
    id="section3"
  >
    {/* Form fields */}
  </FormSection>
</Form>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <div className="space-y-4">
                  <FormSection
                    title={`${t('components.formSections.accordion.group1')} - Section 1`}
                    description={t('components.formSections.accordion.firstSection')}
                    collapsible
                    accordionId="demo-accordion"
                    id="section1"
                    defaultExpanded={expandedSections.accordion1}
                    onExpandChange={expanded => handleSectionExpandChange('accordion1', expanded)}
                  >
                    <FormItem name="firstName" label="First Name">
                      <Input placeholder="Enter first name" fullWidth />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={`${t('components.formSections.accordion.group1')} - Section 2`}
                    description={t('components.formSections.accordion.secondSection')}
                    collapsible
                    accordionId="demo-accordion"
                    id="section2"
                    defaultExpanded={expandedSections.accordion2}
                    onExpandChange={expanded => handleSectionExpandChange('accordion2', expanded)}
                  >
                    <FormItem name="lastName" label="Last Name">
                      <Input placeholder="Enter last name" fullWidth />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={`${t('components.formSections.accordion.group1')} - Section 3`}
                    description={t('components.formSections.accordion.thirdSection')}
                    collapsible
                    accordionId="demo-accordion"
                    id="section3"
                    defaultExpanded={expandedSections.accordion3}
                    onExpandChange={expanded => handleSectionExpandChange('accordion3', expanded)}
                  >
                    <FormItem name="email" label="Email">
                      <Input type="email" placeholder="<EMAIL>" fullWidth />
                    </FormItem>
                  </FormSection>
                </div>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.accordion.title')} className="h-full">
            <div className="space-y-4">
              <p>{t('components.formSections.accordionDesc')}</p>

              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.howItWorks')}
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>{t('components.formSections.accordionWorkingList.oneAtTime')}</li>
                  <li>{t('components.formSections.accordionWorkingList.uniqueId')}</li>
                  <li>{t('components.formSections.accordionWorkingList.sameAccordionId')}</li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-lg mb-2">{t('components.formSections.usage')}</h3>
                <p>{t('components.formSections.accordionUsage')}:</p>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2">
                  {`<FormSection
  collapsible
  accordionId="personal-info"
  id="section1"
  title={\`\${t('components.formSections.accordion.group1')} - Section 1\`}
  description={t('components.formSections.accordion.firstSection')}
>
  {/* Content */}
</FormSection>

<FormSection
  collapsible
  accordionId="personal-info"
  id="section2"
  title={\`\${t('components.formSections.accordion.group1')} - Section 2\`}
  description={t('components.formSections.accordion.secondSection')}
>
  {/* Content */}
</FormSection>`}
                </pre>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Badge and Icon Position Demo */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div>
          <ComponentDemo
            title={t('components.formSections.badge.title')}
            description={t('components.formSections.badge.description')}
            code={`import { Form, FormItem, FormSection, Input, Badge } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

const { t } = useTranslation();

<FormSection
  title={t('components.formSections.badge.title')}
  description={t('components.formSections.badge.description')}
  collapsible
  badge={t('components.formSections.badge.new')}
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.badge.title')}
  description={t('components.formSections.badge.description')}
  collapsible
  badge={<Badge variant="success">{t('components.formSections.badge.required')}</Badge>}
>
  {/* Form fields */}
</FormSection>

<FormSection
  title={t('components.formSections.iconPosition.title')}
  description={t('components.formSections.iconPosition.description')}
  collapsible
  iconPosition="left"
>
  {/* Form fields */}
</FormSection>`}
          >
            <div className="w-full">
              <Form schema={demoSchema} onSubmit={() => {}}>
                <div className="space-y-4">
                  <FormSection
                    title={t('components.formSections.badge.stringBadgeTitle')}
                    description={t('components.formSections.badge.stringBadgeDesc')}
                    collapsible
                    badge={
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        {t('components.formSections.badge.new')}
                      </span>
                    }
                    defaultExpanded={expandedSections.badge}
                    onExpandChange={expanded => handleSectionExpandChange('badge', expanded)}
                  >
                    <FormItem
                      name="firstName"
                      label={t('components.formSections.fields.firstName')}
                    >
                      <Input
                        placeholder={t('components.formSections.placeholders.firstName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.badge.customBadgeTitle')}
                    description={t('components.formSections.badge.customBadgeDesc')}
                    collapsible
                    badge={
                      <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                        {t('components.formSections.badge.required')}
                      </span>
                    }
                  >
                    <FormItem name="lastName" label={t('components.formSections.fields.lastName')}>
                      <Input
                        placeholder={t('components.formSections.placeholders.lastName')}
                        fullWidth
                      />
                    </FormItem>
                  </FormSection>

                  <FormSection
                    title={t('components.formSections.iconPosition.title')}
                    description={t('components.formSections.iconPosition.description')}
                    collapsible
                    iconPosition="right"
                    defaultExpanded={expandedSections.iconPosition}
                    onExpandChange={expanded => handleSectionExpandChange('iconPosition', expanded)}
                  >
                    <div className="flex space-x-4">
                      <FormSection
                        title={t('components.formSections.iconPosition.left')}
                        description={t('components.formSections.iconPosition.leftDesc')}
                        collapsible
                        iconPosition="left"
                        className="flex-1"
                      >
                        <FormItem
                          name="firstName"
                          label={t('components.formSections.fields.firstName')}
                        >
                          <Input
                            placeholder={t('components.formSections.placeholders.firstName')}
                            fullWidth
                          />
                        </FormItem>
                      </FormSection>

                      <FormSection
                        title={t('components.formSections.iconPosition.right')}
                        description={t('components.formSections.iconPosition.rightDesc')}
                        collapsible
                        iconPosition="right"
                        className="flex-1"
                      >
                        <FormItem
                          name="lastName"
                          label={t('components.formSections.fields.lastName')}
                        >
                          <Input
                            placeholder={t('components.formSections.placeholders.lastName')}
                            fullWidth
                          />
                        </FormItem>
                      </FormSection>
                    </div>
                  </FormSection>
                </div>
              </Form>
            </div>
          </ComponentDemo>
        </div>

        <div>
          <Card title={t('components.formSections.badgeAndIcon')} className="h-full">
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.badge.title')}
                </h3>
                <p>{t('components.formSections.badgeDesc')}</p>
                <ul className="list-disc list-inside space-y-1 mt-2">
                  <li>{t('components.formSections.badgeFeatures.stringNumber')}</li>
                  <li>{t('components.formSections.badgeFeatures.defaultBadge')}</li>
                  <li>{t('components.formSections.badgeFeatures.customBadge')}</li>
                </ul>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2">
                  {`<FormSection
  title={t('components.formSections.badge.title')}
  badge={t('components.formSections.badge.new')}
>
  {/* Content */}
</FormSection>

<FormSection
  title={t('components.formSections.badge.title')}
  badge={<Badge variant="success">{t('components.formSections.badge.required')}</Badge>}
>
  {/* Content */}
</FormSection>`}
                </pre>
              </div>

              <div className="mt-6">
                <h3 className="font-medium text-lg mb-2">
                  {t('components.formSections.iconPosition.title')}
                </h3>
                <p>{t('components.formSections.iconPositionDesc')}</p>
                <ul className="list-disc list-inside space-y-1 mt-2">
                  <li>
                    <code>right</code>: {t('components.formSections.iconPositions.right')}
                  </li>
                  <li>
                    <code>left</code>: {t('components.formSections.iconPositions.left')}
                  </li>
                </ul>
                <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm mt-2">
                  {`<FormSection
  title={t('components.formSections.iconPosition.title')}
  collapsible
  iconPosition="left"
>
  {/* Content */}
</FormSection>`}
                </pre>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">{t('components.formSections.title')}</h2>
        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={formRef as any}
          schema={userSchema}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          className="space-y-6"
        >
          {/* Thông tin cá nhân */}
          <FormSection
            title={t('components.formSections.personalInfo')}
            description={t('components.formSections.personalInfoDesc')}
            collapsible
            defaultExpanded={expandedSections.personal}
            onExpandChange={expanded => handleSectionExpandChange('personal', expanded)}
          >
            <FormGrid columns={2} gap="md">
              <FormItem
                name="firstName"
                label={t('components.formSections.fields.firstName')}
                required
              >
                <Input
                  placeholder={t('components.formSections.placeholders.firstName')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="lastName"
                label={t('components.formSections.fields.lastName')}
                required
              >
                <Input placeholder={t('components.formSections.placeholders.lastName')} fullWidth />
              </FormItem>

              <FormItem
                name="email"
                label={t('components.formSections.fields.email')}
                required
                className="col-span-2"
              >
                <Input
                  type="email"
                  placeholder={t('components.formSections.placeholders.email')}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="phone"
                label={t('components.formSections.fields.phone')}
                className="col-span-2"
              >
                <Input placeholder={t('components.formSections.placeholders.phone')} fullWidth />
              </FormItem>
            </FormGrid>
          </FormSection>

          {/* Thông tin công ty */}
          <FormSection
            title={t('components.formSections.companyInfo')}
            description={t('components.formSections.companyInfoDesc')}
            collapsible
            defaultExpanded={expandedSections.company}
            onExpandChange={expanded => handleSectionExpandChange('company', expanded)}
          >
            <FormGrid columns={2} gap="md">
              <FormItem
                name="companyName"
                label={t('components.formSections.fields.companyName')}
                className="col-span-2"
              >
                <Input
                  placeholder={t('components.formSections.placeholders.companyName')}
                  fullWidth
                />
              </FormItem>

              <FormItem name="jobTitle" label={t('components.formSections.fields.jobTitle')}>
                <Input placeholder={t('components.formSections.placeholders.jobTitle')} fullWidth />
              </FormItem>

              <FormItem name="department" label={t('components.formSections.fields.department')}>
                <Input
                  placeholder={t('components.formSections.placeholders.department')}
                  fullWidth
                />
              </FormItem>
            </FormGrid>
          </FormSection>

          <div className="pt-4">
            <Button type="submit" variant="primary">
              {t('components.formSections.buttons.submit')}
            </Button>
          </div>
        </Form>

        {formData && (
          <Card title={t('components.formSections.result')} className="mt-6">
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </Card>
        )}
      </div>
    </div>
  );
};

export default FormSectionsPage;
