/**
 * Pricing Cards Demo Page - Showcase different pricing card designs
 */
import React, { useState } from 'react';
import { Card, Typography, Button } from '@/shared/components/common';
import { SubscriptionDuration, ServiceType, ServicePackage } from '@/modules/subscription/types';

// Import all card components
import {
  ModernPricingCard,
  GlassmorphismCard,
  NeumorphismCard,
  MinimalCard,
  GradientCard,
} from '@/modules/subscription/components/cards';

const PricingCardsPage: React.FC = () => {
  const [selectedDuration, setSelectedDuration] = useState<SubscriptionDuration>(SubscriptionDuration.MONTHLY);
  const [selectedCardType, setSelectedCardType] = useState<string>('modern');

  // Mock data for demonstration
  const mockPackages = [
    {
      id: 'basic',
      name: 'Basic',
      type: ServiceType.MAIN,
      description: 'Perfect for individuals and small projects',
      icon: 'user',
      prices: {
        [SubscriptionDuration.MONTHLY]: 10000,
        [SubscriptionDuration.SEMI_ANNUAL]: 27000,
        [SubscriptionDuration.ANNUAL]: 96000,
      },
      features: [
        { name: 'API Calls', value: '1,000/month' },
        { name: 'Storage', value: '5GB' },
        { name: 'Support', value: 'Email' },
        { name: 'Custom Domain', value: false },
        { name: 'Analytics', value: true },
        { name: 'Team Collaboration', value: false },
        { name: 'SSO Integration', value: false },
      ],
      isPopular: false,
      isRecommended: false,
    },
    {
      id: 'pro',
      name: 'Pro',
      type: ServiceType.MAIN,
      description: 'Best for growing businesses and teams',
      icon: 'users',
      prices: {
        [SubscriptionDuration.MONTHLY]: 25000,
        [SubscriptionDuration.SEMI_ANNUAL]: 67500,
        [SubscriptionDuration.ANNUAL]: 240000,
      },
      features: [
        { name: 'API Calls', value: '10,000/month' },
        { name: 'Storage', value: '50GB' },
        { name: 'Support', value: 'Priority' },
        { name: 'Custom Domain', value: true },
        { name: 'Analytics', value: true },
        { name: 'Team Collaboration', value: true },
      ],
      isPopular: true,
      isRecommended: true,
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      type: ServiceType.MAIN,
      description: 'Advanced features for large organizations',
      icon: 'building',
      prices: {
        [SubscriptionDuration.MONTHLY]: 50000,
        [SubscriptionDuration.SEMI_ANNUAL]: 135000,
        [SubscriptionDuration.ANNUAL]: 480000,
      },
      features: [
        { name: 'API Calls', value: 'Unlimited' },
        { name: 'Storage', value: '500GB' },
        { name: 'Support', value: '24/7 Phone' },
        { name: 'Custom Domain', value: true },
        { name: 'Analytics', value: true },
        { name: 'Team Collaboration', value: true },
        { name: 'SSO Integration', value: true },
      ],
      isPopular: false,
      isRecommended: false,
    },
  ];

  const handleSelectPackage = (pkg: ServicePackage) => {
    console.log('Selected package:', pkg);
  };

  const cardTypes = [
    { id: 'modern', name: 'Modern', component: ModernPricingCard },
    { id: 'glassmorphism', name: 'Glassmorphism', component: GlassmorphismCard },
    { id: 'neumorphism', name: 'Neumorphism', component: NeumorphismCard },
    { id: 'minimal', name: 'Minimal', component: MinimalCard },
    { id: 'gradient', name: 'Gradient', component: GradientCard },
  ];

  const selectedCardComponent = cardTypes.find(type => type.id === selectedCardType)?.component || ModernPricingCard;
  const CardComponent = selectedCardComponent;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <Typography variant="h2" className="text-3xl font-bold mb-4">
          Pricing Cards Showcase
        </Typography>
        <Typography variant="body1" className="text-gray-600 dark:text-gray-400 max-w-3xl">
          Explore different pricing card designs inspired by modern web applications like Stripe, Vercel, and other SaaS platforms.
          Each design offers a unique visual style while maintaining functionality and accessibility.
        </Typography>
      </div>

      {/* Controls */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Card Type Selector */}
          <div>
            <Typography variant="h6" className="font-semibold mb-3">
              Card Design Style
            </Typography>
            <div className="flex flex-wrap gap-2">
              {cardTypes.map((type) => (
                <Button
                  key={type.id}
                  variant={selectedCardType === type.id ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCardType(type.id)}
                >
                  {type.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Duration Selector */}
          <div>
            <Typography variant="h6" className="font-semibold mb-3">
              Billing Period
            </Typography>
            <div className="flex flex-wrap gap-2">
              {Object.values(SubscriptionDuration).map((duration) => (
                <Button
                  key={duration}
                  variant={selectedDuration === duration ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedDuration(duration)}
                >
                  {duration === 'semi_annual' ? 'Semi Annual' : duration.charAt(0).toUpperCase() + duration.slice(1)}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* Cards Grid */}
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        style={{
          display: 'grid',
          alignItems: 'stretch',
          gridTemplateRows: 'repeat(auto-fit, minmax(600px, 1fr))'
        }}
      >
        {mockPackages.map((pkg) => (
          <div
            key={pkg.id}
            className="flex"
            style={{ minHeight: '600px' }}
          >
            <CardComponent
              package={pkg}
              duration={selectedDuration}
              onSelect={handleSelectPackage}
              className="w-full"
            />
          </div>
        ))}
      </div>

      {/* Design Information */}
      <Card className="p-8">
        <Typography variant="h4" className="font-bold mb-6">
          Design Inspirations & Features
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="space-y-3">
            <Typography variant="h6" className="font-semibold text-blue-600">
              🎯 Modern
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              Inspired by Stripe and Vercel. Clean design with subtle shadows, hover effects, and professional appearance.
            </Typography>
          </div>

          <div className="space-y-3">
            <Typography variant="h6" className="font-semibold text-purple-600">
              ✨ Glassmorphism
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              Trendy glass effect with backdrop blur and transparency. Popular in modern UI design with frosted glass appearance.
            </Typography>
          </div>

          <div className="space-y-3">
            <Typography variant="h6" className="font-semibold text-orange-600">
              🎨 Neumorphism
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              Soft UI design with subtle shadows creating a pressed/embossed effect. Minimalist and tactile appearance.
            </Typography>
          </div>

          <div className="space-y-3">
            <Typography variant="h6" className="font-semibold text-gray-600">
              📐 Minimal
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              Clean and simple design inspired by Apple and Linear. Focus on typography, whitespace, and clarity.
            </Typography>
          </div>

          <div className="space-y-3">
            <Typography variant="h6" className="font-semibold text-pink-600">
              🌈 Gradient
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              Vibrant gradient design with dynamic colors and modern visual effects. Eye-catching and energetic.
            </Typography>
          </div>

          <div className="space-y-3">
            <Typography variant="h6" className="font-semibold text-green-600">
              🚀 Features
            </Typography>
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              All cards support dark mode, responsive design, accessibility features, and smooth animations.
            </Typography>
          </div>
        </div>
      </Card>

      {/* Usage Example */}
      <Card className="p-6">
        <Typography variant="h5" className="font-semibold mb-4">
          Usage Example
        </Typography>
        <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
          <pre className="text-sm">
{`import { ModernPricingCard } from '@/modules/subscription/components/cards';

<ModernPricingCard
  package={packageData}
  duration={SubscriptionDuration.MONTHLY}
  onSelect={(pkg) => console.log('Selected:', pkg)}
/>`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default PricingCardsPage;
