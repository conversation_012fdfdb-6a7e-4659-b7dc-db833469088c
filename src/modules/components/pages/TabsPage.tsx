import React, { useState } from 'react';
import { ComponentDemo } from '../components';
import { Tabs, Icon, Button } from '@/shared/components/common';
import type { TabItem } from '@/shared/components/common/Tabs/Tabs';

const TabsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('1');

  // Basic tabs data
  const basicTabs: TabItem[] = [
    {
      key: '1',
      label: 'Tab 1',
      children: <div className="p-4">Nội dung của Tab 1</div>,
    },
    {
      key: '2',
      label: 'Tab 2',
      children: <div className="p-4">Nội dung của Tab 2</div>,
    },
    {
      key: '3',
      label: 'Tab 3',
      children: <div className="p-4">Nội dung của Tab 3</div>,
      disabled: true,
    },
  ];

  // Tabs with icons
  const iconTabs: TabItem[] = [
    {
      key: '1',
      label: 'Trang chủ',
      icon: <Icon name="home" size="sm" />,
      children: <div className="p-4">Nội dung trang chủ</div>,
    },
    {
      key: '2',
      label: 'Cài đặt',
      icon: <Icon name="settings" size="sm" />,
      children: <div className="p-4">Nội dung cài đặt</div>,
    },
    {
      key: '3',
      label: 'Người dùng',
      icon: <Icon name="user" size="sm" />,
      children: <div className="p-4">Nội dung người dùng</div>,
    },
  ];

  // Tabs with badges
  const badgeTabs: TabItem[] = [
    {
      key: '1',
      label: 'Tin nhắn',
      icon: <Icon name="chat" size="sm" />,
      badge: '5',
      badgeColor: 'danger',
      children: <div className="p-4">Bạn có 5 tin nhắn mới</div>,
    },
    {
      key: '2',
      label: 'Thông báo',
      icon: <Icon name="alert-circle" size="sm" />,
      badge: '12',
      badgeColor: 'warning',
      children: <div className="p-4">Bạn có 12 thông báo mới</div>,
    },
    {
      key: '3',
      label: 'Hoàn thành',
      icon: <Icon name="check" size="sm" />,
      badge: '3',
      badgeColor: 'success',
      children: <div className="p-4">Bạn đã hoàn thành 3 nhiệm vụ</div>,
    },
  ];

  // Icon only tabs
  const iconOnlyTabs: TabItem[] = [
    {
      key: '1',
      label: 'Trang chủ',
      icon: <Icon name="home" size="md" />,
      children: <div className="p-4">Nội dung trang chủ</div>,
    },
    {
      key: '2',
      label: 'Cài đặt',
      icon: <Icon name="settings" size="md" />,
      children: <div className="p-4">Nội dung cài đặt</div>,
    },
    {
      key: '3',
      label: 'Người dùng',
      icon: <Icon name="user" size="md" />,
      children: <div className="p-4">Nội dung người dùng</div>,
    },
    {
      key: '4',
      label: 'Biểu đồ',
      icon: <Icon name="chart" size="md" />,
      children: <div className="p-4">Nội dung biểu đồ</div>,
    },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          Tabs Components
        </h1>
        <p className="text-muted">
          Các component Tab với nhiều kiểu dáng và tùy chọn khác nhau
        </p>
      </div>

      {/* Default Tabs */}
      <ComponentDemo
        title="Default Tabs"
        description="Tabs mặc định với đường viền dưới"
        code={`import { Tabs } from '@/shared/components/common';

const items = [
  { key: '1', label: 'Tab 1', children: <div>Nội dung 1</div> },
  { key: '2', label: 'Tab 2', children: <div>Nội dung 2</div> },
  { key: '3', label: 'Tab 3', children: <div>Nội dung 3</div>, disabled: true },
];

<Tabs items={items} />`}
      >
        <Tabs items={basicTabs} />
      </ComponentDemo>

      {/* Card Tabs */}
      <ComponentDemo
        title="Card Tabs"
        description="Tabs với kiểu card"
        code={`<Tabs type="card" items={items} />`}
      >
        <Tabs type="card" items={basicTabs} />
      </ComponentDemo>

      {/* Underline Tabs */}
      <ComponentDemo
        title="Underline Tabs"
        description="Tabs với đường gạch chân động"
        code={`<Tabs type="underline" items={items} />`}
      >
        <Tabs type="underline" items={basicTabs} />
      </ComponentDemo>

      {/* Pills Tabs */}
      <ComponentDemo
        title="Pills Tabs"
        description="Tabs với kiểu viên thuốc"
        code={`<Tabs type="pills" items={items} />`}
      >
        <Tabs type="pills" items={basicTabs} />
      </ComponentDemo>

      {/* Segmented Tabs */}
      <ComponentDemo
        title="Segmented Control"
        description="Tabs với kiểu segmented control"
        code={`<Tabs type="segmented" items={items} />`}
      >
        <Tabs type="segmented" items={basicTabs} />
      </ComponentDemo>

      {/* Minimal Tabs */}
      <ComponentDemo
        title="Minimal Tabs"
        description="Tabs với kiểu tối giản"
        code={`<Tabs type="minimal" items={items} />`}
      >
        <Tabs type="minimal" items={basicTabs} />
      </ComponentDemo>

      {/* Bordered Tabs */}
      <ComponentDemo
        title="Bordered Tabs"
        description="Tabs với viền"
        code={`<Tabs type="bordered" items={items} />`}
      >
        <Tabs type="bordered" items={basicTabs} />
      </ComponentDemo>

      {/* Vertical Card Tabs */}
      <ComponentDemo
        title="Vertical Card Tabs"
        description="Tabs dọc với kiểu card"
        code={`<Tabs type="vertical-card" position="left" items={items} />`}
      >
        <div className="h-64">
          <Tabs type="vertical-card" position="left" items={basicTabs} />
        </div>
      </ComponentDemo>

      {/* Tabs with Icons */}
      <ComponentDemo
        title="Tabs with Icons"
        description="Tabs có icon"
        code={`const iconTabs = [
  {
    key: '1',
    label: 'Trang chủ',
    icon: <Icon name="home" size="sm" />,
    children: <div>Nội dung trang chủ</div>,
  },
  // ...
];

<Tabs items={iconTabs} />`}
      >
        <Tabs items={iconTabs} />
      </ComponentDemo>

      {/* Tabs with Badges */}
      <ComponentDemo
        title="Tabs with Badges"
        description="Tabs có badge thông báo"
        code={`const badgeTabs = [
  {
    key: '1',
    label: 'Tin nhắn',
    icon: <Icon name="chat" size="sm" />,
    badge: '5',
    badgeColor: 'danger',
    children: <div>Nội dung tin nhắn</div>,
  },
  // ...
];

<Tabs items={badgeTabs} />`}
      >
        <Tabs items={badgeTabs} />
      </ComponentDemo>

      {/* Icon Only Tabs */}
      <ComponentDemo
        title="Icon Only Tabs"
        description="Tabs chỉ hiển thị icon"
        code={`<Tabs type="icon-only" items={iconOnlyTabs} />`}
      >
        <Tabs type="icon-only" items={iconOnlyTabs} />
      </ComponentDemo>

      {/* Different Sizes */}
      <ComponentDemo
        title="Different Sizes"
        description="Tabs với các kích thước khác nhau"
        code={`<Tabs size="sm" items={items} />
<Tabs size="md" items={items} />
<Tabs size="lg" items={items} />`}
      >
        <div className="space-y-6">
          <div>
            <h4 className="text-sm font-medium mb-2">Small</h4>
            <Tabs size="sm" items={basicTabs} />
          </div>
          <div>
            <h4 className="text-sm font-medium mb-2">Medium (Default)</h4>
            <Tabs size="md" items={basicTabs} />
          </div>
          <div>
            <h4 className="text-sm font-medium mb-2">Large</h4>
            <Tabs size="lg" items={basicTabs} />
          </div>
        </div>
      </ComponentDemo>

      {/* Controlled Tabs */}
      <ComponentDemo
        title="Controlled Tabs"
        description="Tabs được điều khiển từ bên ngoài"
        code={`const [activeTab, setActiveTab] = useState('1');

<div className="mb-4">
  <Button onClick={() => setActiveTab('2')}>
    Chuyển sang Tab 2
  </Button>
</div>
<Tabs
  activeKey={activeTab}
  onChange={setActiveTab}
  items={items}
/>`}
      >
        <div>
          <div className="mb-4 space-x-2">
            <Button
              size="sm"
              variant={activeTab === '1' ? 'primary' : 'outline'}
              onClick={() => setActiveTab('1')}
            >
              Tab 1
            </Button>
            <Button
              size="sm"
              variant={activeTab === '2' ? 'primary' : 'outline'}
              onClick={() => setActiveTab('2')}
            >
              Tab 2
            </Button>
            <Button
              size="sm"
              variant={activeTab === '3' ? 'primary' : 'outline'}
              onClick={() => setActiveTab('3')}
            >
              Tab 3
            </Button>
          </div>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={basicTabs}
          />
        </div>
      </ComponentDemo>

      {/* Real-world Examples */}
      <div className="mt-12">
        <h2 className="text-lg font-semibold text-foreground mb-6">
          Ví dụ thực tế
        </h2>

        {/* Dashboard Tabs */}
        <ComponentDemo
          title="Dashboard Navigation"
          description="Tabs cho điều hướng dashboard với icon và badge"
          code={`const dashboardTabs = [
  {
    key: 'overview',
    label: 'Tổng quan',
    icon: <Icon name="chart" size="sm" />,
    children: <DashboardOverview />,
  },
  {
    key: 'analytics',
    label: 'Phân tích',
    icon: <Icon name="chart" size="sm" />,
    badge: 'Mới',
    badgeColor: 'success',
    children: <Analytics />,
  },
  // ...
];

<Tabs type="pills" items={dashboardTabs} />`}
        >
          <Tabs
            type="pills"
            items={[
              {
                key: 'overview',
                label: 'Tổng quan',
                icon: <Icon name="chart" size="sm" />,
                children: (
                  <div className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <h3 className="text-lg font-medium mb-4">Dashboard Overview</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-primary">1,234</div>
                        <div className="text-sm text-muted">Tổng người dùng</div>
                      </div>
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">$12,345</div>
                        <div className="text-sm text-muted">Doanh thu</div>
                      </div>
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">89%</div>
                        <div className="text-sm text-muted">Tỷ lệ chuyển đổi</div>
                      </div>
                    </div>
                  </div>
                ),
              },
              {
                key: 'analytics',
                label: 'Phân tích',
                icon: <Icon name="chart" size="sm" />,
                badge: 'Mới',
                badgeColor: 'success',
                children: (
                  <div className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <h3 className="text-lg font-medium mb-4">Analytics Dashboard</h3>
                    <p className="text-muted">Tính năng phân tích nâng cao đang được phát triển...</p>
                  </div>
                ),
              },
              {
                key: 'reports',
                label: 'Báo cáo',
                icon: <Icon name="document" size="sm" />,
                children: (
                  <div className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <h3 className="text-lg font-medium mb-4">Reports</h3>
                    <p className="text-muted">Các báo cáo chi tiết về hiệu suất...</p>
                  </div>
                ),
              },
            ]}
          />
        </ComponentDemo>

        {/* Settings Tabs */}
        <ComponentDemo
          title="Settings Panel"
          description="Tabs cho panel cài đặt với kiểu vertical"
          code={`<Tabs
  type="vertical-card"
  position="left"
  items={settingsTabs}
/>`}
        >
          <div className="h-80">
            <Tabs
              type="vertical-card"
              position="left"
              items={[
                {
                  key: 'profile',
                  label: 'Hồ sơ',
                  icon: <Icon name="user" size="sm" />,
                  children: (
                    <div className="p-6">
                      <h3 className="text-lg font-medium mb-4">Thông tin hồ sơ</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">Tên hiển thị</label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            defaultValue="Nguyễn Văn A"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Email</label>
                          <input
                            type="email"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            defaultValue="<EMAIL>"
                          />
                        </div>
                      </div>
                    </div>
                  ),
                },
                {
                  key: 'security',
                  label: 'Bảo mật',
                  icon: <Icon name="lock" size="sm" />,
                  children: (
                    <div className="p-6">
                      <h3 className="text-lg font-medium mb-4">Cài đặt bảo mật</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span>Xác thực 2 bước</span>
                          <Button size="sm" variant="outline">Bật</Button>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Đăng nhập bằng sinh trắc học</span>
                          <Button size="sm" variant="outline">Cấu hình</Button>
                        </div>
                      </div>
                    </div>
                  ),
                },
                {
                  key: 'notifications',
                  label: 'Thông báo',
                  icon: <Icon name="alert-circle" size="sm" />,
                  badge: '3',
                  badgeColor: 'warning',
                  children: (
                    <div className="p-6">
                      <h3 className="text-lg font-medium mb-4">Cài đặt thông báo</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span>Email thông báo</span>
                          <input type="checkbox" defaultChecked />
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Push notifications</span>
                          <input type="checkbox" defaultChecked />
                        </div>
                        <div className="flex items-center justify-between">
                          <span>SMS thông báo</span>
                          <input type="checkbox" />
                        </div>
                      </div>
                    </div>
                  ),
                },
              ]}
            />
          </div>
        </ComponentDemo>

        {/* Mobile-friendly Tabs */}
        <ComponentDemo
          title="Mobile Navigation"
          description="Tabs tối ưu cho mobile với icon only"
          code={`<Tabs
  type="icon-only"
  items={mobileNavTabs}
  className="md:hidden"
/>`}
        >
          <div className="max-w-sm mx-auto">
            <Tabs
              type="icon-only"
              items={[
                {
                  key: 'home',
                  label: 'Trang chủ',
                  icon: <Icon name="home" size="lg" />,
                  children: (
                    <div className="p-4 text-center">
                      <Icon name="home" size="xl" className="mx-auto mb-2 text-primary" />
                      <h3 className="font-medium">Trang chủ</h3>
                      <p className="text-sm text-muted">Chào mừng bạn trở lại!</p>
                    </div>
                  ),
                },
                {
                  key: 'search',
                  label: 'Tìm kiếm',
                  icon: <Icon name="search" size="lg" />,
                  children: (
                    <div className="p-4 text-center">
                      <Icon name="search" size="xl" className="mx-auto mb-2 text-primary" />
                      <h3 className="font-medium">Tìm kiếm</h3>
                      <p className="text-sm text-muted">Tìm kiếm nội dung...</p>
                    </div>
                  ),
                },
                {
                  key: 'notifications',
                  label: 'Thông báo',
                  icon: <Icon name="alert-circle" size="lg" />,
                  children: (
                    <div className="p-4 text-center">
                      <Icon name="alert-circle" size="xl" className="mx-auto mb-2 text-primary" />
                      <h3 className="font-medium">Thông báo</h3>
                      <p className="text-sm text-muted">Không có thông báo mới</p>
                    </div>
                  ),
                },
                {
                  key: 'profile',
                  label: 'Hồ sơ',
                  icon: <Icon name="user" size="lg" />,
                  children: (
                    <div className="p-4 text-center">
                      <Icon name="user" size="xl" className="mx-auto mb-2 text-primary" />
                      <h3 className="font-medium">Hồ sơ</h3>
                      <p className="text-sm text-muted">Quản lý tài khoản của bạn</p>
                    </div>
                  ),
                },
              ]}
            />
          </div>
        </ComponentDemo>
      </div>
    </div>
  );
};

export default TabsPage;
