import React, { useRef, useState } from 'react';
import { z } from 'zod';
import {
  Button,
  Card,
  Form,
  FormItem,
  FormArray,
  FormGrid,
  FormSection,
  Input,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

// Schema cho form contacts
const contactSchema = z.object({
  contacts: z.array(
    z.object({
      name: z.string().min(1, 'Tên là bắt buộc'),
      email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
      phone: z.string().optional().or(z.literal('')),
    })
  ),
});

// Schema cho form education
const educationSchema = z.object({
  education: z.array(
    z.object({
      school: z.string().min(1, 'Tên trường là bắt buộc'),
      degree: z.string().min(1, 'Bằng cấp là bắt buộc'),
      fieldOfStudy: z.string().optional().or(z.literal('')),
      startYear: z
        .string()
        .regex(/^\d{4}$/, 'Năm bắt đầu phải có 4 chữ số')
        .refine(val => {
          const year = parseInt(val, 10);
          return year >= 1900 && year <= new Date().getFullYear();
        }, 'Năm bắt đầu không hợp lệ'),
      endYear: z
        .string()
        .regex(/^\d{4}$/, 'Năm kết thúc phải có 4 chữ số')
        .refine(val => {
          const year = parseInt(val, 10);
          return year >= 1900 && year <= new Date().getFullYear() + 10;
        }, 'Năm kết thúc không hợp lệ')
        .optional()
        .or(z.literal('')),
      description: z.string().optional().or(z.literal('')),
    })
  ),
});

// Schema cho form products
const productSchema = z.object({
  products: z.array(
    z.object({
      name: z.string().min(1, 'Tên sản phẩm là bắt buộc'),
      price: z
        .string()
        .min(1, 'Giá là bắt buộc')
        .regex(/^\d+(\.\d{1,2})?$/, 'Giá không hợp lệ'),
      quantity: z
        .string()
        .min(1, 'Số lượng là bắt buộc')
        .regex(/^\d+$/, 'Số lượng phải là số nguyên'),
      category: z.string().min(1, 'Danh mục là bắt buộc'),
      description: z.string().optional().or(z.literal('')),
    })
  ),
});

// Định nghĩa kiểu dữ liệu từ schema
type ContactFormValues = z.infer<typeof contactSchema>;
type EducationFormValues = z.infer<typeof educationSchema>;
type ProductFormValues = z.infer<typeof productSchema>;

/**
 * Trang demo cho Form Array
 */
const FormArrayDemo: React.FC = () => {
  // Refs cho các form
  const contactFormRef = useRef<FormRef<ContactFormValues>>(null);
  const educationFormRef = useRef<FormRef<EducationFormValues>>(null);
  const productFormRef = useRef<FormRef<ProductFormValues>>(null);

  // State để lưu dữ liệu form
  const [contactFormData, setContactFormData] = useState<ContactFormValues | null>(null);
  const [educationFormData, setEducationFormData] = useState<EducationFormValues | null>(null);
  const [productFormData, setProductFormData] = useState<ProductFormValues | null>(null);

  // Xử lý submit form contacts
  const handleContactSubmit = (values: ContactFormValues) => {
    console.log('Contact form submitted:', values);
    setContactFormData(values);
  };

  // Xử lý submit form education
  const handleEducationSubmit = (values: EducationFormValues) => {
    console.log('Education form submitted:', values);
    setEducationFormData(values);
  };

  // Xử lý submit form products
  const handleProductSubmit = (values: ProductFormValues) => {
    console.log('Product form submitted:', values);
    setProductFormData(values);
  };

  // Danh sách danh mục sản phẩm
  const productCategories = [
    { value: 'electronics', label: 'Điện tử' },
    { value: 'clothing', label: 'Quần áo' },
    { value: 'books', label: 'Sách' },
    { value: 'food', label: 'Thực phẩm' },
    { value: 'toys', label: 'Đồ chơi' },
  ];

  return (
    <div className="space-y-8">
      <Card title="Form Array Demo" className="mb-6">
        <p className="mb-4">Demo tính năng quản lý mảng các field trong form (dynamic fields).</p>
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
          <h3 className="text-sm font-medium mb-2">Tính năng chính:</h3>
          <ul className="list-disc list-inside text-sm space-y-1 text-gray-600 dark:text-gray-400">
            <li>Thêm/xóa field động</li>
            <li>Validation cho từng item trong mảng</li>
            <li>Hỗ trợ nested objects trong mảng</li>
            <li>Drag and drop để sắp xếp lại các item</li>
            <li>Số lượng item tối thiểu/tối đa</li>
          </ul>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Demo 1: Danh sách liên hệ */}
        <Card title="Danh sách liên hệ" className="mb-6">
          <Form
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ref={contactFormRef as any}
            schema={contactSchema}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSubmit={handleContactSubmit as any}
            className="space-y-6"
            defaultValues={{
              contacts: [{ name: '', email: '', phone: '' }],
            }}
          >
            <FormArray
              name="contacts"
              title="Danh sách liên hệ"
              description="Thêm các liên hệ của bạn"
              addButtonText="Thêm liên hệ"
              defaultValue={{ name: '', email: '', phone: '' }}
              minItems={1}
              renderItem={index => (
                <FormGrid columns={2} gap="md">
                  <FormItem name={`contacts.${index}.name`} label="Tên" required>
                    <Input placeholder="Nhập tên" fullWidth />
                  </FormItem>

                  <FormItem name={`contacts.${index}.email`} label="Email">
                    <Input type="email" placeholder="Nhập email" fullWidth />
                  </FormItem>

                  <FormItem
                    name={`contacts.${index}.phone`}
                    label="Số điện thoại"
                    className="col-span-2"
                  >
                    <Input placeholder="Nhập số điện thoại" fullWidth />
                  </FormItem>
                </FormGrid>
              )}
            />

            <div className="pt-4">
              <Button type="submit" variant="primary">
                Lưu danh sách liên hệ
              </Button>
            </div>
          </Form>

          {contactFormData && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
              <h4 className="font-medium mb-2">Kết quả:</h4>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(contactFormData, null, 2)}
              </pre>
            </div>
          )}
        </Card>

        {/* Demo 2: Quá trình học tập */}
        <Card title="Quá trình học tập" className="mb-6">
          <Form
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ref={educationFormRef as any}
            schema={educationSchema}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSubmit={handleEducationSubmit as any}
            className="space-y-6"
            defaultValues={{
              education: [
                {
                  school: '',
                  degree: '',
                  fieldOfStudy: '',
                  startYear: '',
                  endYear: '',
                  description: '',
                },
              ],
            }}
          >
            <FormArray
              name="education"
              title="Quá trình học tập"
              description="Thêm các bằng cấp và quá trình học tập của bạn"
              addButtonText="Thêm quá trình học tập"
              defaultValue={{
                school: '',
                degree: '',
                fieldOfStudy: '',
                startYear: '',
                endYear: '',
                description: '',
              }}
              minItems={1}
              renderItem={index => (
                <FormSection
                  title={`Quá trình học tập ${index + 1}`}
                  collapsible
                  defaultExpanded={index === 0}
                >
                  <FormGrid columns={2} gap="md">
                    <FormItem
                      name={`education.${index}.school`}
                      label="Trường"
                      required
                      className="col-span-2"
                    >
                      <Input placeholder="Nhập tên trường" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.degree`} label="Bằng cấp" required>
                      <Input placeholder="Nhập bằng cấp" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.fieldOfStudy`} label="Ngành học">
                      <Input placeholder="Nhập ngành học" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.startYear`} label="Năm bắt đầu" required>
                      <Input placeholder="Ví dụ: 2018" fullWidth />
                    </FormItem>

                    <FormItem name={`education.${index}.endYear`} label="Năm kết thúc">
                      <Input placeholder="Ví dụ: 2022" fullWidth />
                    </FormItem>

                    <FormItem
                      name={`education.${index}.description`}
                      label="Mô tả"
                      className="col-span-2"
                    >
                      <textarea
                        className="w-full p-2 border rounded"
                        placeholder="Mô tả quá trình học tập"
                      />
                    </FormItem>
                  </FormGrid>
                </FormSection>
              )}
            />

            <div className="pt-4">
              <Button type="submit" variant="primary">
                Lưu quá trình học tập
              </Button>
            </div>
          </Form>

          {educationFormData && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
              <h4 className="font-medium mb-2">Kết quả:</h4>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(educationFormData, null, 2)}
              </pre>
            </div>
          )}
        </Card>

        {/* Demo 3: Danh sách sản phẩm */}
        <Card title="Danh sách sản phẩm" className="mb-6">
          <Form
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ref={productFormRef as any}
            schema={productSchema}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSubmit={handleProductSubmit as any}
            className="space-y-6"
            defaultValues={{
              products: [
                {
                  name: '',
                  price: '',
                  quantity: '',
                  category: '',
                  description: '',
                },
              ],
            }}
          >
            <FormArray
              name="products"
              title="Danh sách sản phẩm"
              description="Thêm các sản phẩm của bạn"
              addButtonText="Thêm sản phẩm"
              defaultValue={{
                name: '',
                price: '',
                quantity: '',
                category: '',
                description: '',
              }}
              minItems={1}
              renderItem={index => (
                <FormGrid columns={2} gap="md">
                  <FormItem
                    name={`products.${index}.name`}
                    label="Tên sản phẩm"
                    required
                    className="col-span-2"
                  >
                    <Input placeholder="Nhập tên sản phẩm" fullWidth />
                  </FormItem>

                  <FormItem name={`products.${index}.price`} label="Giá" required>
                    <Input placeholder="Nhập giá" fullWidth />
                  </FormItem>

                  <FormItem name={`products.${index}.quantity`} label="Số lượng" required>
                    <Input placeholder="Nhập số lượng" fullWidth />
                  </FormItem>

                  <FormItem
                    name={`products.${index}.category`}
                    label="Danh mục"
                    required
                    className="col-span-2"
                  >
                    <select className="w-full p-2 border rounded">
                      <option value="">Chọn danh mục</option>
                      {productCategories.map(category => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                  </FormItem>

                  <FormItem
                    name={`products.${index}.description`}
                    label="Mô tả"
                    className="col-span-2"
                  >
                    <textarea className="w-full p-2 border rounded" placeholder="Mô tả sản phẩm" />
                  </FormItem>
                </FormGrid>
              )}
            />

            <div className="pt-4">
              <Button type="submit" variant="primary">
                Lưu danh sách sản phẩm
              </Button>
            </div>
          </Form>

          {productFormData && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
              <h4 className="font-medium mb-2">Kết quả:</h4>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(productFormData, null, 2)}
              </pre>
            </div>
          )}
        </Card>
      </div>

      <Card title="Hướng dẫn sử dụng" className="mb-6">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">FormArray Component</h3>
            <p className="mb-2">Component để quản lý mảng các field trong form.</p>
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
              {`<FormArray
  name="contacts"
  title="Danh sách liên hệ"
  description="Thêm các liên hệ của bạn"
  addButtonText="Thêm liên hệ"
  defaultValue={{ name: '', email: '', phone: '' }}
  minItems={1}
  renderItem={(index, field, remove) => (
    <FormGrid columns={2} gap="md">
      <FormItem
        name={\`contacts.\${index}.name\`}
        label="Tên"
        required
      >
        <Input placeholder="Nhập tên" fullWidth />
      </FormItem>
      {/* Các field khác */}
    </FormGrid>
  )}
/>`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-2">Validation</h3>
            <p className="mb-2">Validation cho mảng và các item trong mảng.</p>
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
              {`const schema = z.object({
  contacts: z.array(
    z.object({
      name: z.string().min(1, 'Tên là bắt buộc'),
      email: z.string().email('Email không hợp lệ').optional(),
      phone: z.string().optional(),
    })
  ),
});`}
            </pre>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FormArrayDemo;
