import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComponentDemo } from '../../components';
import { Select, Icon, Grid, Typography } from '@/shared/components/common';
import type { SelectOption, SelectGroup } from '@/shared/components/common/Select/Select';

/**
 * Trang demo cho component Select
 */
const SelectDemo: React.FC = () => {
  const { t } = useTranslation();
  const [singleValue, setSingleValue] = useState<string>('');
  const [multiValue, setMultiValue] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  const [loadingValue, setLoadingValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Options cho single select
  const singleOptions: SelectOption[] = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'orange', label: 'Orange' },
    { value: 'grape', label: 'Grape' },
    { value: 'watermelon', label: 'Watermelon' },
  ];

  // Options cho multi select
  const multiOptions: SelectOption[] = [
    { value: 'react', label: 'React', icon: <Icon name="code" size="sm" /> },
    { value: 'vue', label: 'Vue', icon: <Icon name="code" size="sm" /> },
    { value: 'angular', label: 'Angular', icon: <Icon name="code" size="sm" /> },
    { value: 'svelte', label: 'Svelte', icon: <Icon name="code" size="sm" /> },
    { value: 'nextjs', label: 'Next.js', icon: <Icon name="code" size="sm" /> },
  ];

  // Options cho searchable select
  const searchOptions: SelectOption[] = [
    { value: 'afghanistan', label: 'Afghanistan' },
    { value: 'albania', label: 'Albania' },
    { value: 'algeria', label: 'Algeria' },
    { value: 'andorra', label: 'Andorra' },
    { value: 'angola', label: 'Angola' },
    { value: 'argentina', label: 'Argentina' },
    { value: 'armenia', label: 'Armenia' },
    { value: 'australia', label: 'Australia' },
    { value: 'austria', label: 'Austria' },
    { value: 'azerbaijan', label: 'Azerbaijan' },
    // Thêm nhiều quốc gia khác...
    { value: 'vietnam', label: 'Vietnam' },
  ];

  // Options cho grouped select
  const groupedOptions: (SelectOption | SelectGroup)[] = [
    {
      label: 'Fruits',
      options: [
        { value: 'apple', label: 'Apple' },
        { value: 'banana', label: 'Banana' },
        { value: 'orange', label: 'Orange' },
      ],
    },
    {
      label: 'Vegetables',
      options: [
        { value: 'carrot', label: 'Carrot' },
        { value: 'broccoli', label: 'Broccoli' },
        { value: 'cucumber', label: 'Cucumber' },
      ],
    },
    {
      label: 'Berries',
      options: [
        { value: 'strawberry', label: 'Strawberry' },
        { value: 'blueberry', label: 'Blueberry' },
        { value: 'raspberry', label: 'Raspberry' },
      ],
    },
  ];

  // Options cho loading select
  const loadingOptions: SelectOption[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  // Simulate loading
  const handleLoadingClick = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  // Custom rendering cho options
  const renderCustomOption = (option: SelectOption) => {
    return (
      <div className="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-lighter cursor-pointer">
        <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-2">
          {option.label.charAt(0)}
        </div>
        <div>
          <div className="font-medium">{option.label}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Custom option</div>
        </div>
      </div>
    );
  };

  // Options cho custom rendering
  const customOptions: SelectOption[] = [
    { value: 'user1', label: 'John Doe' },
    { value: 'user2', label: 'Jane Smith' },
    { value: 'user3', label: 'Robert Johnson' },
    { value: 'user4', label: 'Emily Davis' },
  ];

  return (
    <div className="space-y-8">
      <Typography variant="h1">{t('components.inputs.select.title')}</Typography>
      <Typography>{t('components.inputs.select.description')}</Typography>

      {/* Single Select */}
      <ComponentDemo
        title={t('components.inputs.singleSelect.title')}
        description={t('components.inputs.singleSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Options
const options = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'orange', label: 'Orange' },
  { value: 'grape', label: 'Grape' },
  { value: 'watermelon', label: 'Watermelon' },
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select a fruit"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={options}
  placeholder="Choose a fruit"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select a fruit"
            value={singleValue}
            onChange={val => setSingleValue(val as string)}
            options={singleOptions}
            placeholder="Choose a fruit"
          />
          {singleValue && (
            <div className="mt-2 text-sm">
              Selected value: <span className="font-medium">{singleValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Multi Select */}
      <ComponentDemo
        title={t('components.inputs.multiSelect.title')}
        description={t('components.inputs.multiSelect.description')}
        code={`import { Select, Icon } from '@/shared/components/common';

// Options with icons
const options = [
  { value: 'react', label: 'React', icon: <Icon name="code" size="sm" /> },
  { value: 'vue', label: 'Vue', icon: <Icon name="code" size="sm" /> },
  { value: 'angular', label: 'Angular', icon: <Icon name="code" size="sm" /> },
  { value: 'svelte', label: 'Svelte', icon: <Icon name="code" size="sm" /> },
  { value: 'nextjs', label: 'Next.js', icon: <Icon name="code" size="sm" /> },
];

// State
const [value, setValue] = useState<string[]>([]);

// Render
<Select
  label="Select frameworks"
  value={value}
  onChange={(val) => setValue(val as string[])}
  options={options}
  multiple
  placeholder="Choose frameworks"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select frameworks"
            value={multiValue}
            onChange={val => setMultiValue(val as string[])}
            options={multiOptions}
            multiple
            placeholder="Choose frameworks"
          />
          {multiValue.length > 0 && (
            <div className="mt-2 text-sm">
              Selected values:
              <span className="font-medium">{multiValue.join(', ')}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Searchable Select */}
      <ComponentDemo
        title={t('components.inputs.searchableSelect.title')}
        description={t('components.inputs.searchableSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Many options
const countryOptions = [
  { value: 'afghanistan', label: 'Afghanistan' },
  { value: 'albania', label: 'Albania' },
  // ... many more countries
  { value: 'vietnam', label: 'Vietnam' },
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select a country"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={countryOptions}
  searchable
  placeholder="Search countries..."
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select a country"
            value={searchValue}
            onChange={val => setSearchValue(val as string)}
            options={searchOptions}
            searchable
            placeholder="Search countries..."
          />
          {searchValue && (
            <div className="mt-2 text-sm">
              Selected country: <span className="font-medium">{searchValue}</span>
            </div>
          )}
        </div>
      </ComponentDemo>

      {/* Grouped Select */}
      <ComponentDemo
        title={t('components.inputs.groupedSelect.title')}
        description={t('components.inputs.groupedSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Grouped options
const groupedOptions = [
  {
    label: 'Fruits',
    options: [
      { value: 'apple', label: 'Apple' },
      { value: 'banana', label: 'Banana' },
      { value: 'orange', label: 'Orange' },
    ],
  },
  {
    label: 'Vegetables',
    options: [
      { value: 'carrot', label: 'Carrot' },
      { value: 'broccoli', label: 'Broccoli' },
      { value: 'cucumber', label: 'Cucumber' },
    ],
  },
  // ... more groups
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select food"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={groupedOptions}
  placeholder="Choose food"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select food"
            value={singleValue}
            onChange={val => setSingleValue(val as string)}
            options={groupedOptions}
            placeholder="Choose food"
          />
        </div>
      </ComponentDemo>

      {/* Loading Select */}
      <ComponentDemo
        title={t('components.inputs.loadingSelect.title')}
        description={t('components.inputs.loadingSelect.description')}
        code={`import { Select, Button } from '@/shared/components/common';
import { useState } from 'react';

// State
const [value, setValue] = useState('');
const [isLoading, setIsLoading] = useState(false);

// Simulate loading
const handleLoadingClick = () => {
  setIsLoading(true);
  setTimeout(() => {
    setIsLoading(false);
  }, 2000);
};

// Render
<>
  <Select
    label="Loading state demo"
    value={value}
    onChange={(val) => setValue(val as string)}
    options={options}
    loading={isLoading}
    placeholder="Select an option"
  />
  <Button onClick={handleLoadingClick} className="mt-2">
    Simulate Loading
  </Button>
</>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Loading state demo"
            value={loadingValue}
            onChange={val => setLoadingValue(val as string)}
            options={loadingOptions}
            loading={isLoading}
            placeholder="Select an option"
          />
          <button
            onClick={handleLoadingClick}
            className="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Simulate Loading
          </button>
        </div>
      </ComponentDemo>

      {/* Custom Rendering */}
      <ComponentDemo
        title={t('components.inputs.customRenderingSelect.title')}
        description={t('components.inputs.customRenderingSelect.description')}
        code={`import { Select } from '@/shared/components/common';

// Custom render function
const renderCustomOption = (option) => (
  <div className="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-lighter cursor-pointer">
    <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-2">
      {option.label.charAt(0)}
    </div>
    <div>
      <div className="font-medium">{option.label}</div>
      <div className="text-xs text-gray-500 dark:text-gray-400">Custom option</div>
    </div>
  </div>
);

// Options
const customOptions = [
  { value: 'user1', label: 'John Doe' },
  { value: 'user2', label: 'Jane Smith' },
  { value: 'user3', label: 'Robert Johnson' },
  { value: 'user4', label: 'Emily Davis' },
];

// State
const [value, setValue] = useState('');

// Render
<Select
  label="Select user"
  value={value}
  onChange={(val) => setValue(val as string)}
  options={customOptions}
  renderOption={renderCustomOption}
  placeholder="Choose a user"
/>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Select
            label="Select user"
            value={singleValue}
            onChange={val => setSingleValue(val as string)}
            options={customOptions}
            renderOption={renderCustomOption}
            placeholder="Choose a user"
          />
        </div>
      </ComponentDemo>

      {/* Sizes and Variants */}
      <ComponentDemo
        title="Select Sizes"
        description="Select với các kích thước khác nhau"
        code={`import { Select, Grid } from '@/shared/components/common';

<Grid columns={1} gap="md">
  <Select
    label="Small Select"
    options={options}
    placeholder="Small size"
    size="sm"
  />

  <Select
    label="Medium Select (Default)"
    options={options}
    placeholder="Medium size"
    size="md"
  />

  <Select
    label="Large Select"
    options={options}
    placeholder="Large size"
    size="lg"
  />
</Grid>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Grid columns={1} columnGap="md" rowGap="md">
            <Select
              label="Small Select"
              options={singleOptions}
              placeholder="Small size"
              size="sm"
            />

            <Select
              label="Medium Select (Default)"
              options={singleOptions}
              placeholder="Medium size"
              size="md"
            />

            <Select
              label="Large Select"
              options={singleOptions}
              placeholder="Large size"
              size="lg"
            />
          </Grid>
        </div>
      </ComponentDemo>

      {/* Error and Helper Text */}
      <ComponentDemo
        title="Select with Error and Helper Text"
        description="Select với error và helper text"
        code={`import { Select, Grid } from '@/shared/components/common';

<Grid columns={1} gap="md">
  <Select
    label="Select with Helper Text"
    options={options}
    placeholder="Choose an option"
    helperText="This is a helper text"
  />

  <Select
    label="Select with Error"
    options={options}
    placeholder="Choose an option"
    error="This field is required"
  />
</Grid>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Grid columns={1} columnGap="md" rowGap="md">
            <Select
              label="Select with Helper Text"
              options={singleOptions}
              placeholder="Choose an option"
              helperText="This is a helper text"
            />

            <Select
              label="Select with Error"
              options={singleOptions}
              placeholder="Choose an option"
              error="This field is required"
            />
          </Grid>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default SelectDemo;
