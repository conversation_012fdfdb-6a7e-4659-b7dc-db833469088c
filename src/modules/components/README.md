# Hướng dẫn sử dụng Components trong RedAI Frontend

Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng các component trong thư mục `src/modules/components` và cách áp dụng chúng vào các trang thực tế.

## <PERSON>ụ<PERSON> lục

1. [Giới thiệu](#giới-thiệu)
2. [C<PERSON>u trúc thư mục](#cấu-trúc-thư-mục)
3. [MenuIconBar Component](#menuiconbar-component)
   - [Tính năng](#tính-năng)
   - [Props](#props)
   - [Ví dụ sử dụng](#ví-dụ-sử-dụng)
   - [Tích hợp với useDataTable](#tích-hợp-với-usedatatable)
4. [SlideInForm Component](#slideinform-component)
5. [Các Component khác](#các-component-khác)
6. [Thực hành: AffiliateRankListPageOptimized](#thực-hành-affiliateranklistpageoptimized)
7. [Các mẫu thiết kế phổ biến](#các-mẫu-thiết-kế-phổ-biến)
8. [Câu hỏi thường gặp](#câu-hỏi-thường-gặp)

## Giới thiệu

Thư mục `src/modules/components` chứa các component tùy chỉnh được thiết kế đặc biệt cho RedAI Frontend. Các component này được xây dựng dựa trên các component cơ bản từ `shared/components/common` nhưng cung cấp các tính năng và giao diện phức tạp hơn, phù hợp với các yêu cầu cụ thể của ứng dụng.

## Cấu trúc thư mục

```
src/modules/components/
├── buttons/        # Các component button tùy chỉnh
├── card/           # Các component card tùy chỉnh
├── components/     # Các component phức tạp khác
├── examples/       # Ví dụ sử dụng các component
├── locales/        # Các file ngôn ngữ
├── menu-bar/       # Các component thanh menu
│   ├── MenuIconBar.tsx           # Component thanh icon chức năng
│   └── MenuIconBarExample.tsx    # Ví dụ sử dụng MenuIconBar
├── otp/            # Các component liên quan đến OTP
├── pages/          # Các component trang
└── index.ts        # File export các component
```

## MenuIconBar Component

`MenuIconBar` là một component thanh công cụ đa năng, cung cấp các tính năng phổ biến như tìm kiếm, thêm mới, lọc theo menu, lọc theo khoảng thời gian và tùy chỉnh hiển thị cột.

### Tính năng

- **Tìm kiếm**: Hiển thị thanh tìm kiếm khi nhấp vào icon tìm kiếm
- **Thêm mới**: Gọi hàm callback khi nhấp vào icon thêm mới
- **Lọc theo menu**: Hiển thị menu lọc với các tùy chọn tùy chỉnh
- **Lọc theo khoảng thời gian**: Hiển thị bộ chọn khoảng thời gian
- **Tùy chỉnh hiển thị cột**: Cho phép người dùng chọn các cột hiển thị

### Props

| Prop                       | Kiểu dữ liệu                                    | Mô tả                                             |
| -------------------------- | ----------------------------------------------- | ------------------------------------------------- |
| `onSearch`                 | `(term: string) => void`                        | Callback khi người dùng tìm kiếm                  |
| `onAdd`                    | `() => void`                                    | Callback khi người dùng nhấp vào icon thêm mới    |
| `items`                    | `ModernMenuItem[]`                              | Danh sách các mục trong menu lọc                  |
| `onDateRangeChange`        | `(range: [Date \| null, Date \| null]) => void` | Callback khi người dùng thay đổi khoảng thời gian |
| `onColumnVisibilityChange` | `(columns: ColumnVisibility[]) => void`         | Callback khi người dùng thay đổi hiển thị cột     |
| `columns`                  | `ColumnVisibility[]`                            | Danh sách các cột và trạng thái hiển thị          |
| `columnLabelMap`           | `ColumnLabelMap`                                | Map nhãn cho các cột                              |
| `tableColumns`             | `TableColumnInfo[]`                             | Thông tin cột từ Table component                  |
| `showDateFilter`           | `boolean`                                       | Hiển thị bộ lọc khoảng thời gian                  |
| `showColumnFilter`         | `boolean`                                       | Hiển thị bộ lọc cột                               |

### Ví dụ sử dụng

```tsx
<MenuIconBar
  onSearch={term => console.log('Search term:', term)}
  onAdd={() => console.log('Add button clicked')}
  items={[
    {
      id: 'all',
      label: 'Tất cả',
      icon: 'list',
      onClick: () => console.log('All selected'),
    },
    {
      id: 'active',
      label: 'Hoạt động',
      icon: 'check',
      onClick: () => console.log('Active selected'),
    },
    {
      id: 'inactive',
      label: 'Không hoạt động',
      icon: 'eye-off',
      onClick: () => console.log('Inactive selected'),
    },
  ]}
  onDateRangeChange={range => console.log('Date range:', range)}
  onColumnVisibilityChange={columns => console.log('Columns:', columns)}
  columns={[
    { id: 'all', label: 'Tất cả', visible: true },
    { id: 'name', label: 'Tên', visible: true },
    { id: 'status', label: 'Trạng thái', visible: true },
  ]}
  showDateFilter={true}
  showColumnFilter={true}
/>
```

### Tích hợp với useDataTable

`MenuIconBar` được thiết kế để hoạt động liền mạch với hook `useDataTable` từ `shared/hooks/table`:

```tsx
const dataTable = useDataTable(
  useDataTableConfig<AffiliateRankDto, AffiliateRankQueryDto>({
    columns,
    filterOptions,
    showDateFilter: true,
    createQueryParams,
  })
);

<MenuIconBar
  onSearch={dataTable.tableData.handleSearch}
  onAdd={handleAdd}
  items={dataTable.menuItems}
  onDateRangeChange={dataTable.dateRange.setDateRange}
  onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
  columns={dataTable.columnVisibility.visibleColumns}
  showDateFilter={true}
  showColumnFilter={true}
/>;
```

## SlideInForm Component

`SlideInForm` là một component hiển thị form trượt vào từ bên phải màn hình, thường được sử dụng cho các form thêm mới hoặc chỉnh sửa.

### Props

| Prop        | Kiểu dữ liệu      | Mô tả                                   |
| ----------- | ----------------- | --------------------------------------- |
| `isVisible` | `boolean`         | Trạng thái hiển thị của form            |
| `children`  | `React.ReactNode` | Nội dung form                           |
| `width`     | `string`          | Chiều rộng của form (mặc định: '500px') |
| `onClose`   | `() => void`      | Callback khi đóng form                  |

### Ví dụ sử dụng

```tsx
const { isVisible, showForm, hideForm } = useSlideForm();

<SlideInForm isVisible={isVisible}>
  <AffiliateRankForm onSubmit={handleSubmit} onCancel={hideForm} />
</SlideInForm>;
```

## Thực hành: AffiliateRankListPageOptimized

File `AffiliateRankListPageOptimized.tsx` là một ví dụ tuyệt vời về cách sử dụng các component trong thư mục `src/modules/components` kết hợp với các hook từ `shared/hooks/table`.

### Cấu trúc trang

1. **Khai báo hooks và state**:

   ```tsx
   // Sử dụng hook animation cho form
   const { isVisible, showForm, hideForm } = useSlideForm();

   // Định nghĩa columns cho bảng
   const columns = useMemo<TableColumn<AffiliateRankDto>[]>(() => [...], [t]);

   // Tạo filterOptions
   const filterOptions = useMemo(() => [...], [t]);

   // Tạo hàm createQueryParams
   const createQueryParams = (params: {...}) => {...};

   // Sử dụng hook useDataTable với cấu hình mặc định
   const dataTable = useDataTable(
     useDataTableConfig<AffiliateRankDto, AffiliateRankQueryDto>({...})
   );

   // Gọi API lấy danh sách rank với queryParams từ dataTable
   const { data: rankData, isLoading } = useRanks(dataTable.queryParams);
   ```

2. **Xử lý sự kiện**:

   ```tsx
   // Xử lý thêm mới
   const handleAdd = () => {
     showForm();
   };

   // Xử lý submit form
   const handleSubmit = (values: Record<string, unknown>) => {
     console.log('Form values:', values);
     hideForm();
   };

   // Xử lý hủy form
   const handleCancel = () => {
     hideForm();
   };
   ```

3. **Render UI**:

   ```tsx
   return (
     <div>
       <MenuIconBar
         onSearch={dataTable.tableData.handleSearch}
         onAdd={handleAdd}
         items={dataTable.menuItems}
         onDateRangeChange={dataTable.dateRange.setDateRange}
         onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
         columns={dataTable.columnVisibility.visibleColumns}
         showDateFilter={true}
         showColumnFilter={true}
       />

       <SlideInForm isVisible={isVisible}>
         <AffiliateRankForm onSubmit={handleSubmit} onCancel={handleCancel} />
       </SlideInForm>

       <Card className="overflow-hidden">
         <Table
           columns={dataTable.columnVisibility.visibleTableColumns}
           data={rankData?.items || []}
           rowKey="id"
           loading={isLoading}
           sortable={true}
           onSortChange={dataTable.tableData.handleSortChange}
           pagination={{...}}
         />
       </Card>
     </div>
   );
   ```

### Luồng dữ liệu

1. `useDataTable` tạo ra các state và hàm xử lý cho bảng
2. `useRanks` gọi API với `dataTable.queryParams` để lấy dữ liệu
3. `MenuIconBar` sử dụng các hàm xử lý từ `dataTable` để tìm kiếm, lọc, v.v.
4. `Table` hiển thị dữ liệu từ API và sử dụng các hàm xử lý từ `dataTable` để phân trang, sắp xếp, v.v.
5. `SlideInForm` hiển thị form thêm mới khi `isVisible` là `true`

## Các mẫu thiết kế phổ biến

1. **Tách biệt dữ liệu và UI**: Sử dụng hooks để quản lý dữ liệu và logic, component để hiển thị UI
2. **Tái sử dụng logic**: Sử dụng hooks tùy chỉnh như `useDataTable`, `useSlideForm` để tái sử dụng logic
3. **Tùy chỉnh component**: Sử dụng props để tùy chỉnh hành vi và giao diện của component
4. **Callback pattern**: Sử dụng callback để giao tiếp giữa component cha và con

## Câu hỏi thường gặp

### 1. Làm thế nào để tùy chỉnh giao diện của MenuIconBar?

`MenuIconBar` sử dụng các component từ `shared/components/common` như `IconCard`, `Tooltip`, `SearchBar`, v.v. Bạn có thể tùy chỉnh giao diện bằng cách truyền các props phù hợp cho các component này.

### 2. Làm thế nào để thêm tính năng mới vào MenuIconBar?

Nếu bạn cần thêm tính năng mới vào `MenuIconBar`, bạn có thể tạo một component mới kế thừa từ `MenuIconBar` hoặc tạo một component wrapper bao quanh `MenuIconBar`.

### 3. Làm thế nào để tích hợp MenuIconBar với Redux?

Bạn có thể tích hợp `MenuIconBar` với Redux bằng cách truyền các action creator và selector từ Redux làm props cho `MenuIconBar`.

### 4. Làm thế nào để tùy chỉnh SlideInForm?

`SlideInForm` chấp nhận các props như `width`, `onClose` để tùy chỉnh hành vi và giao diện. Bạn cũng có thể truyền bất kỳ component nào làm `children` cho `SlideInForm`.

### 5. Làm thế nào để tạo một trang mới sử dụng các component này?

Để tạo một trang mới, bạn có thể sử dụng `AffiliateRankListPageOptimized.tsx` làm mẫu và thay thế các phần cụ thể cho trang của bạn.
