{"components": {"library": {"title": "<PERSON><PERSON>ư viện Components", "description": "Các component có sẵn trong RedAI Frontend Template."}, "form": {"wizard": {"title": "Form Wizard", "description": "FormWizard component quản lý form nhiều bước với validation và navigation.", "basic": {"title": "Form <PERSON> c<PERSON> bản", "description": "Form wizard c<PERSON> bản với 3 bước và validation."}}}, "charts": {"demo": {"title": "Demo Biểu đồ", "description": "Các component biểu đồ hỗ trợ responsive, đa ngôn ngữ, và theme."}, "lineChart": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ đường", "description": "LineChart component hiển thị dữ liệu dạng đường, hỗ trợ nhiều đường dữ liệu, tooltip, và legend.", "basic": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ đư<PERSON><PERSON> c<PERSON> bản", "description": "<PERSON>i<PERSON>u đồ đường cơ bản với một đường dữ liệu."}, "multiLine": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ nhiều đường", "description": "Biểu đồ đường với nhiều đường dữ liệu."}, "customized": {"title": "<PERSON><PERSON><PERSON>u đồ đường tùy chỉnh", "description": "Biểu đồ đường với các tùy chỉnh như loại đường, đ<PERSON> dày, và hiển thị điểm dữ liệu."}}}, "animation": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hi<PERSON>u ứng động có sẵn trong RedAI Frontend Template."}, "notification": {"title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o (Notification)", "description": "<PERSON><PERSON>n thị thông báo cho người dùng về trạng thái của hành động hoặc thông tin quan trọng.", "basic": {"title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON> bản", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i thông báo cơ bản với các kiểu kh<PERSON>c nhau."}, "hook": {"title": "Sử dụng hook useNotification", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông báo bằng hook useNotification."}, "hideCode": "Ẩn code", "showCode": "Hiển thị code"}, "banner": {"title": "Banner", "description": "Component <PERSON> hiển thị nội dung nổi bật với nhiều tùy chọn.", "basic": {"title": "<PERSON> c<PERSON> bản", "description": "Banner cơ bản với tiêu đề và mô tả."}, "withBackground": {"title": "Banner v<PERSON><PERSON> h<PERSON>nh <PERSON>n", "description": "Banner v<PERSON><PERSON> hình nền và overlay."}, "gradient": {"title": "Banner với gradient", "description": "Banner với nền gradient và các nút hành động."}, "wave": {"title": "Banner v<PERSON>i hiệu <PERSON> sóng", "description": "Banner với hiệu ứng sóng ở dưới cùng."}, "custom": {"title": "Banner với nội dung tùy chỉnh", "description": "Banner với nội dung tùy chỉnh thay vì sử dụng title và description.", "exploreButton": "Khám phá ngay"}, "borderRadius": {"title": "Banner với border radius", "description": "Banner với các tùy chọn border radius khác nhau.", "topCorners": {"title": "Banner với border radius ở 2 góc trên", "description": "Sử dụng thuộc tính borderRadius='rounded-t-xl'"}, "allCorners": {"title": "Banner với border radius ở tất cả các góc", "description": "Sử dụng thuộc tính borderRadius='rounded-xl'"}, "bottomCorners": {"title": "Banner với border radius ở 2 góc dưới", "description": "Sử dụng thuộc tính borderRadius='rounded-b-xl'"}}}, "categories": {"buttons": {"title": "Buttons", "description": "<PERSON><PERSON><PERSON> lo<PERSON> nút bấm khác nhau: primary, secondary, outline, icon buttons..."}, "cards": {"title": "Cards", "description": "<PERSON><PERSON><PERSON> card hiển thị nội dung, thông tin, d<PERSON> li<PERSON>u..."}, "chips": {"title": "Chips", "description": "Chips là các phần tử nhỏ gọn thể hiện một đầu vào, thu<PERSON><PERSON> t<PERSON>, hoặc hành động..."}, "inputs": {"title": "Inputs", "description": "<PERSON><PERSON><PERSON> input: text, number, checkbox, radio, select..."}, "layout": {"title": "Layout Components", "description": "Các component bố cục: container, grid, flex, resizer..."}, "theme": {"title": "Theme Components", "description": "Các component liên quan đến theme: theme toggle, language selector...", "system": {"title": "<PERSON><PERSON> thống Theme", "description": "<PERSON><PERSON> thống theme mới với khả năng tùy chỉnh và mở rộng"}}, "form": {"title": "Form Components", "description": "Các component form: input, select, checkbox, radio...", "theme": {"title": "Form với Theme System", "description": "Demo các component form sử dụng hệ thống theme mới"}}, "typography": {"title": "Typography", "description": "Các component định dạng văn bản để hiển thị nhất quán trong ứng dụng."}}, "grid": {"title": "Grid", "description": "Component <PERSON>rid gi<PERSON>p tạo layout dạng lưới linh hoạt và responsive."}, "responsiveGrid": {"title": "Responsive Grid", "description": "Grid responsive nâng cao tự động điều chỉnh dựa trên kích thước màn hình và trạng thái chat panel."}, "menu": {"title": "<PERSON><PERSON>", "description": "<PERSON>u với nhiều tính năng: submenu, các mode khác nhau, collapsed state"}, "tooltip": {"title": "<PERSON><PERSON><PERSON>", "description": "Tooltip hiển thị thông tin bổ sung khi hover vào phần tử"}, "searchBar": {"title": "Search Bar", "description": "<PERSON><PERSON> tìm kiếm với hiệu ứng animation và nhiều kiểu dáng khác nhau"}, "modernMenu": {"title": "Modern Menu", "description": "<PERSON>u hiện đại với nhiều kiểu dáng và vị trí khác nhau"}, "cards": {"title": "Cards", "description": "<PERSON><PERSON><PERSON> card hiển thị nội dung, thông tin, dữ li<PERSON>u trong hệ thống RedAI."}, "avatar": {"title": "Avatar", "description": "Component <PERSON><PERSON> hiển thị hình ảnh đại diện của người dùng."}, "imageGallery": {"title": "Image Gallery", "description": "Component hiển thị bộ sưu tập hình <PERSON>nh với nhiều tùy chọn."}, "topCard": {"title": "Top Card", "description": "Component hiển thị thông tin tổng quan dạng card."}, "simpleChart": {"title": "Simple Chart", "description": "<PERSON><PERSON><PERSON><PERSON> đồ đơn giản sử dụng trự<PERSON> tiếp thư viện Recharts"}, "moduleGallery": {"title": "Module Gallery", "description": "<PERSON><PERSON>á<PERSON> phá và truy cập các module trong hệ thống", "search": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON>ếm module...", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy module nào", "noResultsDescription": "Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc"}, "filters": {"category": "<PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "categories": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "business": "<PERSON><PERSON>", "marketing": "Marketing", "data": "<PERSON><PERSON> liệu", "tools": "<PERSON><PERSON><PERSON> cụ", "rpoint": "R-Point"}, "sizes": {"sm": "Nhỏ", "md": "<PERSON>rung bình", "lg": "Lớn"}, "stats": {"showing": "<PERSON><PERSON>n thị {{count}} / {{total}} modules", "sizeLabel": "<PERSON><PERSON><PERSON> th<PERSON>: {{size}}"}}}}